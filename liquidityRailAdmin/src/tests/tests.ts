// test.ts

import kotaniPay from "../helpers/kotani";
import Ogateway from "../helpers/Ogateway";
import CNGNUtility from "../helpers/CNGNUtility";
import { Network } from "cngn-typescript-library";
import MudaPayment from "../helpers/MudaPayment";
import Accounts from "../models/accounts";
import Quidax from "../helpers/Quidax";
import { WebhookSender } from "../helpers/webhookSender";

export async function runTests() {
  try {

    const webhook = await WebhookSender.send("4cbad511592843ba822ed6a76d89858f");
    console.log('Webhook:', webhook);
    

   // const swap2 = await new Accounts().swapQuidax('usdc', 'ngn', 1, '89890')
   // console.log('Swap2:', swap2);

 /*
   const data = {
      amount: 10,
      symbol: 'USDC_BSC',
      currency: 'UGX',
      provider_id: 1,
      
    }

    const rate = await new Accounts().bookRate(data);
    console.log('Rate:', rate);

    const confirmRate = {
      quote_id: rate.data.quoteId,
      reference_id: rate.data.quoteId,
      payment_method_id: "19a5dcfa60afb4fa08f7ce36c91e02f47",
      sending_address: "******************************************",
      chain: "BSC",
      company_id:1001,
      service_id:1000,
      source:"exchange"
    }
 

    const confirmRateResponse = await new Accounts().confirmBookedRate(confirmRate);
    console.log('Confirm Rate:', confirmRateResponse);
    */ 
   
    return

   // const response = await MudaPayment.makePayout("456789", 600, "************");
   // console.log("Calling appHealth()...", response);

    const testTransaction = {

    };
    // const ogate = await Ogateway.sendToMobile("MUDA-4567890", 200, "JAMES OKELLO", "**********")
    // console.log('Test Transaction:', ogate);

    // const resp1 = await Quidax.getPaymentAddress('me', 'usdt');

    // console.log('Test Transaction:', resp1);

    // const resp = await MudaPayment.makePayout('test-ref-123',5000,'************');
    //  console.log(`resp`, resp)
    //const healthResponse = await kotaniPay.appHealth();

    const network = Network.xbn
    //  const balance =  await new CNGNUtility().generateWalletAddress(network)
    // console.log(`balances`,balance)

    const requestData: any = {
      mobileMoney: {
        providerNetwork: 'MTN',
        phoneNumber: '+************',
        accountName: 'mbonye'
      },
      currency: 'UGX',
      chain: 'STELLAR',
      token: 'USDC',
      fiatAmount: 10000,
      receiverAddress: 'GDEJYFRWPZIA4NIJD7FFDNQ6UQHCM3AL6SINMHKUHZ2T5B5YJE2NW2CF',
      referenceId: 'kotani'
    };


    // const healthResponse = await kotaniPay.getOfframpStatus("muda-107");
    //const healthResponse = await kotaniPay.getOnrampStatus("muda-1234'");
    //


    // const healthResponse = await kotaniPay.kotaniOnRamp(requestData);
    //const healthResponse = await kotaniPay.getWalletFiats();

    //const healthResponse = await kotaniPay.transfer("65a245d1a6561a60b1d14711",10000);


    const mobileMoney = {
      "customer_key": "ejJQaqbRhPgkEtWpLYX9",
      "amount": 5000,
      "wallet_id": "65a245d1a6561a60b1d14711",
      "callback_url": "https://muda.tech",
      "reference_id": "56789"
    }
    //  const healthResponse = await kotaniPay.createDepositMobileMoney(mobileMoney);


    const mobileMoneyW = {
      "customer_key": "ejJQaqbRhPgkEtWpLYX9",
      "amount": 5000,
      "walletId": "65a245d1a6561a60b1d14711",
      "callbackUrl": "https://muda.tech",
      "referenceId": "56789"
    }
    //  const healthResponse = await kotaniPay.createWithdrawMobileMoney(mobileMoneyW);


    //const healthResponse = await kotaniPay.createMobileMoneyOfframp("","","xlm","");


    //  const healthResponse = await kotaniPay.createCustomerMobileMoney("+254728519101","KE","MPESA","SULEIMAN MURUNGA");





    // const healthResponse = await kotaniPay.getApiKeyHash();

    //const healthResponse = await kotaniPay.getOffRampRate("USDT", "KES", 1);

    //   const healthResponse = await kotaniPay.login("<EMAIL>");
    //console.log("Health Response:", healthResponse);

    // Uncomment and customize the following tests as needed:
    /*
    console.log("Calling login()...");
    const loginResponse = await kotaniPay.login({
      body: { username: "your_username", password: "your_password" }
    });
    console.log("Login Response:", loginResponse);

    console.log("Calling getInterator()...");
    const integratorResponse = await kotaniPay.getInterator({
      token: "your_access_token"
    });
    console.log("Integrator Response:", integratorResponse);
    */

  } catch (error) {
    console.error("Test Error:", error);
  }
}

