
export interface ProviderService {
    provider_name: string;
    provider_id: number;
    service_id: number;
    service_code: string;
    service_name: string;
    min_amount: number;
    max_amount: number;
    fee: string;
    fee_type: string;
    currency: string;
    rate: number;
    accepted_assets: string[];
}


export interface RateRequest {
    amount: number | string;
    currency: string;
    asset_code: string;
    service_code?: string;
    provider_id?: number;
}

export interface RateResponse {
    provider: string;
    providerQuoteId: string;
    from: string;
    providerId: number;
    to: string;
    fiatAmount: number | string;
    toAmount: number | string;
    cryptoAmount: number | string;
    fee: number | string;
    quoteId: string;
    expiresAt?: string;
    quotedPrice?: number | string;
    // allow additional properties returned by third-party providers
    [key: string]: any;
} 