import axios, { AxiosResponse } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import dotenv from 'dotenv';
import logger from '../helpers/logger';
import Model, { Steps } from '../helpers/model';

dotenv.config();

// Tembo API Response Interfaces
interface TemboBaseResponse {
  success?: boolean;
  message?: string;
  statusCode?: string | number;
  reason?: string;
}

interface TemboVirtualAccountResponse extends TemboBaseResponse {
  id?: string;
  accountName?: string;
  accountNo?: string;
  reference?: string;
  tag?: string;
}

interface TemboBalanceResponse extends TemboBaseResponse {
  result?: {
    accountNo: string;
    accountName: string;
    branchCode: string;
    availableBalance: number;
    bookedBalance: number;
  };
}

interface TemboStatementResponse extends TemboBaseResponse {
  result?: {
    accountNo: string;
    statement: Array<{
      id: string;
      transactionId: string;
      reference: string;
      transactionType: string;
      channel: string;
      transactionDate: string;
      postingDate: string;
      valueDate: string;
      narration: string;
      currency: string;
      amountCredit: number;
      amountDebit: number;
      clearedBalance: number;
      bookedBalance: number;
    }>;
  };
}

interface TemboCollectionResponse extends TemboBaseResponse {
  transactionRef?: string;
  transactionId?: string;
}

interface TemboPaymentResponse extends TemboBaseResponse {
  transactionRef?: string;
  transactionId?: string;
}

// Webhook payload interface
interface TemboWebhookPayload {
  accountNo: string;
  payerName?: string;
  id: string;
  transactionId: string;
  reference: string;
  transactionType: string;
  channel: string;
  transactionDate: string;
  postingDate: string;
  valueDate: string;
  narration: string;
  currency: string;
  amountCredit: number;
  amountDebit: number;
  clearedBalance: number;
  bookedBalance: number;
}

// Standard response format following existing patterns
interface StandardResponse {
  status: number;
  message?: string;
  trans_id?: string;
  amount_transfered?: number;
  transaction_fee?: number;
  data?: any;
}

/**
 * TemboService - Comprehensive wrapper for Tembo API endpoints
 * Follows existing integration patterns from PegPay, MyFX, Quidax
 */
export default class TemboService extends Model {
  private baseURL: string;
  private authToken: string;
  private accountId: string;
  private secretKey: string;
  private webhookSecret: string;

  constructor() {
    super();
    this.baseURL = process.env.TEMBO_API_URL || 'https://sandbox.temboplus.com';
    this.authToken = process.env.TEMBO_AUTH_TOKEN || '';
    this.accountId = process.env.TEMBO_ACCOUNT_ID || '';
    this.secretKey = process.env.TEMBO_SECRET_KEY || '';
    this.webhookSecret = process.env.TEMBO_WEBHOOK_SECRET || '';
  }

  /**
   * Generate request headers following Tembo API requirements
   * Reuses pattern from Quidax integration
   */
  private getHeaders(includeAuth: boolean = true): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'x-request-id': uuidv4()
    };

    if (includeAuth) {
      if (this.authToken) {
        headers['Authorization'] = `Bearer ${this.authToken}`;
      }
      if (this.accountId) {
        headers['x-account-id'] = this.accountId;
      }
      if (this.secretKey) {
        headers['x-secret-key'] = this.secretKey;
      }
    }

    return headers;
  }

  /**
   * Make HTTP request with error handling
   * Follows pattern from existing integrations
   */
  private async makeRequest(
    method: 'GET' | 'POST',
    endpoint: string,
    data?: any,
    useCollectionAuth: boolean = false
  ): Promise<any> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const headers = useCollectionAuth ? 
        this.getCollectionHeaders() : 
        this.getHeaders();

      logger.info(`🔹 Tembo ${method} Request`, { url, data });

      let response: AxiosResponse;
      
      if (method === 'POST') {
        response = await axios.post(url, data, { headers });
      } else {
        response = await axios.get(url, { headers });
      }

      logger.info(`✅ Tembo Response`, { 
        status: response.status, 
        data: response.data 
      });

      return response.data;
    } catch (error: any) {
      logger.error(`❌ Tembo API Error`, {
        endpoint,
        error: error.response?.data || error.message,
        status: error.response?.status
      });

      // Return standardized error response
      return {
        success: false,
        statusCode: error.response?.status || 500,
        message: error.response?.data?.message || error.message,
        data: error.response?.data
      };
    }
  }

  /**
   * Get collection-specific headers (different auth pattern)
   */
  private getCollectionHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'x-account-id': this.accountId,
      'x-secret-key': this.secretKey,
      'x-request-id': uuidv4()
    };
  }

  /**
   * Normalize response to standard format
   * Follows pattern from ThirdPartyHandler
   */
  private normalizeResponse(
    temboResponse: any, 
    transactionId?: string,
    amount?: number
  ): StandardResponse {
    // Success cases
    if (temboResponse.success || 
        temboResponse.statusCode === 'PENDING_ACK' || 
        temboResponse.statusCode === 'PAYMENT_ACCEPTED' ||
        temboResponse.statusCode === 201) {
      return {
        status: 200,
        message: temboResponse.message || 'Transaction successful',
        trans_id: temboResponse.transactionRef || temboResponse.transactionId || transactionId,
        amount_transfered: amount,
        data: temboResponse
      };
    }

    // Error cases
    return {
      status: typeof temboResponse.statusCode === 'number' ? 
        temboResponse.statusCode : 500,
      message: temboResponse.message || temboResponse.reason || 'Transaction failed',
      data: temboResponse
    };
  }

  /**
   * Log transaction to database
   * Reuses pattern from existing integrations
   */
  private async logTransaction(
    transId: string,
    step: Steps,
    status: number,
    message: string,
    data: any
  ): Promise<void> {
    try {
      await this.saveTransactionLog(transId, "TEMBO", step, status, message, data);
    } catch (error) {
      logger.error('Failed to log transaction', { transId, error });
    }
  }

  /**
   * Detect mobile network from phone number
   * Follows pattern from PegPay integration
   */
  private detectMobileNetwork(phone: string): string {
    const cleanPhone = phone.replace(/\+/g, '');
    
    // Tanzania mobile prefixes
    if (cleanPhone.startsWith('25575') || cleanPhone.startsWith('25576') || cleanPhone.startsWith('25577')) {
      return 'TZ-VODACOM-C2B'; // M-Pesa
    }
    if (cleanPhone.startsWith('25578') || cleanPhone.startsWith('25579')) {
      return 'TZ-AIRTEL-C2B';
    }
    if (cleanPhone.startsWith('25571') || cleanPhone.startsWith('25572')) {
      return 'TZ-TIGO-C2B';
    }
    
    // Default to Tigo for unknown numbers
    return 'TZ-TIGO-C2B';
  }

  /**
   * Get payout network code
   */
  private getPayoutNetwork(phone: string): string {
    const cleanPhone = phone.replace(/\+/g, '');

    if (cleanPhone.startsWith('25575') || cleanPhone.startsWith('25576') || cleanPhone.startsWith('25577')) {
      return 'TZ-VODACOM-B2C';
    }
    if (cleanPhone.startsWith('25578') || cleanPhone.startsWith('25579')) {
      return 'TZ-AIRTEL-B2C';
    }
    if (cleanPhone.startsWith('25571') || cleanPhone.startsWith('25572')) {
      return 'TZ-TIGO-B2C';
    }

    return 'TZ-TIGO-B2C';
  }

  // ==================== VIRTUAL ACCOUNT METHODS ====================

  /**
   * Create Virtual Account for merchant
   * Maps to: POST /account
   */
  async createVirtualAccount(
    merchantId: string,
    metadata: { companyName: string; tag?: string }
  ): Promise<StandardResponse> {
    const transId = `VA_${merchantId}_${Date.now()}`;

    try {
      await this.logTransaction(transId, Steps.SEND_TO_THIRD_PARTY, 202, "Creating virtual account", metadata);

      const payload = {
        companyName: metadata.companyName,
        reference: `MUDA_${merchantId}_${Date.now()}`,
        tag: metadata.tag || process.env?.TEMBO_DEFAULT_TAG || 'MUDA'
      };

      const response: TemboVirtualAccountResponse = await this.makeRequest(
        'POST',
        '/account',
        payload
      );

      const standardResponse = this.normalizeResponse(response, transId);

      await this.logTransaction(
        transId,
        Steps.THIRDPARTY_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Virtual account response',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(transId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  /**
   * Get Virtual Account Balance
   * Maps to: POST /account/balance
   */
  async getVirtualAccountBalance(accountNo: string): Promise<StandardResponse> {
    const transId = `BAL_${accountNo}_${Date.now()}`;

    try {
      await this.logTransaction(transId, Steps.SEND_TO_THIRD_PARTY, 202, "Getting account balance", { accountNo });

      const payload = { accountNo };
      const response: TemboBalanceResponse = await this.makeRequest(
        'POST',
        '/account/balance',
        payload
      );

      const standardResponse = this.normalizeResponse(response, transId);

      await this.logTransaction(
        transId,
        Steps.THIRDPARTY_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Balance retrieved',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(transId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  /**
   * Get Virtual Account Statement
   * Maps to: POST /account/statement
   */
  async getVirtualAccountStatement(
    accountNo: string,
    startDate: string,
    endDate: string
  ): Promise<StandardResponse> {
    const transId = `STMT_${accountNo}_${Date.now()}`;

    try {
      await this.logTransaction(transId, Steps.SEND_TO_THIRD_PARTY, 202, "Getting account statement", {
        accountNo, startDate, endDate
      });

      const payload = { accountNo, startDate, endDate };
      const response: TemboStatementResponse = await this.makeRequest(
        'POST',
        '/account/statement',
        payload
      );

      const standardResponse = this.normalizeResponse(response, transId);

      await this.logTransaction(
        transId,
        Steps.THIRDPARTY_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Statement retrieved',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(transId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  // ==================== COLLECTION METHODS ====================

  /**
   * Collect from Mobile Money Wallet
   * Maps to: POST /tembo/v1/collection
   * Follows pattern from PegPay.makeMMPullRequest
   */
  async collectFromWallet(
    transactionId: string,
    phone: string,
    amount: number,
    currency: string = 'TZS',
    narration?: string
  ): Promise<StandardResponse> {
    try {
      await this.logTransaction(transactionId, Steps.SEND_TO_THIRD_PARTY, 202, "Initiating mobile money collection", {
        phone, amount, currency
      });

      // Auto-detect network from phone number (following PegPay pattern)
      const channel = this.detectMobileNetwork(phone);
      const cleanPhone = phone.replace(/\+/g, '');

      const payload = {
        msisdn: cleanPhone,
        channel: channel,
        amount: amount,
        narration: narration || `Collection for ${transactionId}`,
        transactionRef: transactionId,
        transactionDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
        callbackUrl: `${process.env?.WEBHOOK_BASE_URL}/webhook/tembo`
      };

      const response: TemboCollectionResponse = await this.makeRequest(
        'POST',
        '/tembo/v1/collection',
        payload,
        true // Use collection auth headers
      );

      const standardResponse = this.normalizeResponse(response, transactionId, amount);

      await this.logTransaction(
        transactionId,
        Steps.THIRDPARTY_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Collection initiated',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(transactionId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  /**
   * Collect from Bank Account (not directly supported by Tembo)
   * This would typically route through virtual accounts
   */
  async collectFromBank(
    transactionId: string,
    accountNumber: string,
    bankCode: string,
    amount: number,
    currency: string = 'TZS'
  ): Promise<StandardResponse> {
    // For now, return not supported - bank collections go through virtual accounts
    return {
      status: 501,
      message: 'Bank collections not directly supported. Use virtual accounts instead.',
      data: { transactionId, accountNumber, bankCode, amount, currency }
    };
  }

  // ==================== PAYOUT METHODS ====================

  /**
   * Pay to Mobile Money Wallet
   * Maps to: POST /tembo/v1/payment/wallet-to-mobile
   * Follows pattern from PegPay.makeMMPushRequest
   */
  async payToMobile(
    transactionId: string,
    accountNo: string,
    phone: string,
    amount: number,
    recipientName: string,
    currency: string = 'TZS',
    narration?: string
  ): Promise<StandardResponse> {
    try {
      await this.logTransaction(transactionId, Steps.SEND_TO_THIRD_PARTY, 202, "Initiating mobile money payout", {
        phone, amount, currency, accountNo
      });

      // Auto-detect network for payout
      const serviceCode = this.getPayoutNetwork(phone);
      const cleanPhone = phone.replace(/\+/g, '');

      const payload = {
        countryCode: 'TZ',
        accountNo: accountNo,
        serviceCode: serviceCode,
        amount: amount,
        msisdn: cleanPhone,
        narration: narration || `Payout for ${transactionId}`,
        currencyCode: currency,
        recipientNames: recipientName,
        transactionRef: transactionId,
        transactionDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
        callbackUrl: `${process.env?.WEBHOOK_BASE_URL}/webhook/tembo`
      };

      const response: TemboPaymentResponse = await this.makeRequest(
        'POST',
        '/tembo/v1/payment/wallet-to-mobile',
        payload,
        true // Use collection auth headers
      );

      const standardResponse = this.normalizeResponse(response, transactionId, amount);

      await this.logTransaction(
        transactionId,
        Steps.THIRDPARTY_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Payout initiated',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(transactionId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  // ==================== BALANCE & TRANSACTION METHODS ====================

  /**
   * Get Collection Balance
   * Maps to: GET /tembo/v1/collection/balance (if available)
   */
  async getCollectionBalance(): Promise<StandardResponse> {
    const transId = `COL_BAL_${Date.now()}`;

    try {
      await this.logTransaction(transId, Steps.SEND_TO_THIRD_PARTY, 202, "Getting collection balance", {});

      // Note: This endpoint might not exist in Tembo API
      // Using main balance as fallback
      const response = await this.makeRequest(
        'GET',
        '/tembo/v1/collection/balance',
        undefined,
        true
      );

      const standardResponse = this.normalizeResponse(response, transId);

      await this.logTransaction(
        transId,
        Steps.THIRDPARTY_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Collection balance retrieved',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(transId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  /**
   * Get Transaction Status
   * Maps to: GET /tembo/v1/payment/status/{transactionId} or similar
   */
  async getTransactionStatus(transactionRef: string): Promise<StandardResponse> {
    const transId = `STATUS_${transactionRef}_${Date.now()}`;

    try {
      await this.logTransaction(transId, Steps.TRANS_STATUS_CHECK, 202, "Checking transaction status", { transactionRef });

      // Note: Exact endpoint may vary - check Tembo docs
      const response = await this.makeRequest(
        'GET',
        `/tembo/v1/payment/status/${transactionRef}`,
        undefined,
        true
      );

      const standardResponse = this.normalizeResponse(response, transId);

      await this.logTransaction(
        transId,
        Steps.TRANS_STATUS_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Status retrieved',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(transId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  // ==================== WEBHOOK METHODS ====================

  /**
   * Verify webhook signature
   * Follows Tembo's HMAC SHA-256 signature verification
   */
  verifyWebhookSignature(
    payload: TemboWebhookPayload,
    timestamp: string,
    receivedSignature: string
  ): boolean {
    try {
      if (!this.webhookSecret) {
        logger.warn('Webhook secret not configured');
        return false;
      }

      // Decode the secret from base64
      const secret = Buffer.from(this.webhookSecret, 'base64');

      // Reconstruct the concatenated string as per Tembo docs
      const concatenatedString =
        timestamp +
        payload.accountNo +
        payload.id +
        payload.transactionId +
        payload.reference +
        payload.transactionType +
        payload.channel +
        payload.transactionDate +
        payload.postingDate +
        payload.valueDate +
        payload.narration +
        payload.currency +
        Math.trunc(payload.amountCredit).toString() +
        Math.trunc(payload.amountDebit).toString() +
        Math.trunc(payload.clearedBalance).toString() +
        Math.trunc(payload.bookedBalance).toString();

      // Compute HMAC signature
      const hmac = crypto.createHmac('sha256', secret);
      hmac.update(Buffer.from(concatenatedString, 'utf-8'));
      const computedSignature = hmac.digest('base64');

      return computedSignature === receivedSignature;
    } catch (error: any) {
      logger.error('Webhook signature verification failed', { error: error.message });
      return false;
    }
  }

  /**
   * Process webhook payload
   * Normalizes webhook data to standard format
   */
  async processWebhook(
    payload: TemboWebhookPayload,
    signature: string,
    timestamp: string
  ): Promise<StandardResponse> {
    try {
      // Verify signature
      const isValid = this.verifyWebhookSignature(payload, timestamp, signature);
      if (!isValid) {
        return {
          status: 401,
          message: 'Invalid webhook signature',
          data: payload
        };
      }

      await this.logTransaction(
        payload.reference,
        Steps.WEBHOOK_RECEIVED,
        200,
        'Webhook received and verified',
        payload
      );

      // Determine transaction type and amount
      const isCredit = payload.amountCredit > 0;
      const amount = isCredit ? payload.amountCredit : payload.amountDebit;
      const transactionType = isCredit ? 'CREDIT' : 'DEBIT';

      // Return standardized webhook data
      return {
        status: 200,
        message: 'Webhook processed successfully',
        trans_id: payload.reference,
        amount_transfered: amount,
        data: {
          ...payload,
          transaction_type: transactionType,
          is_credit: isCredit,
          processed_at: new Date().toISOString()
        }
      };
    } catch (error: any) {
      logger.error('Webhook processing failed', { error: error.message, payload });
      return {
        status: 500,
        message: 'Webhook processing failed',
        data: { error: error.message, payload }
      };
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Validate phone number format for Tanzania
   * Follows pattern from PegPay integration
   */
  validatePhoneNumber(phone: string): { valid: boolean; message?: string } {
    const cleanPhone = phone.replace(/[\s\-\+]/g, '');

    // Tanzania phone numbers should start with 255 and be 12 digits
    if (!cleanPhone.startsWith('255')) {
      return { valid: false, message: 'Phone number must start with 255 (Tanzania country code)' };
    }

    if (cleanPhone.length !== 12) {
      return { valid: false, message: 'Phone number must be 12 digits including country code' };
    }

    // Check if it matches known network prefixes
    const network = this.detectMobileNetwork(cleanPhone);
    if (!network.includes('TZ-')) {
      return { valid: false, message: 'Unsupported mobile network' };
    }

    return { valid: true };
  }

  /**
   * Format amount for Tembo API
   */
  formatAmount(amount: number): number {
    // Tembo expects amounts in minor units (e.g., cents for TZS)
    return Math.round(amount * 100) / 100;
  }

  /**
   * Get supported currencies
   */
  getSupportedCurrencies(): string[] {
    return ['TZS']; // Currently only Tanzanian Shilling
  }

  /**
   * Get supported mobile networks
   */
  getSupportedNetworks(): string[] {
    return [
      'TZ-TIGO-C2B', 'TZ-TIGO-B2C',
      'TZ-AIRTEL-C2B', 'TZ-AIRTEL-B2C',
      'TZ-VODACOM-C2B', 'TZ-VODACOM-B2C'
    ];
  }
}
