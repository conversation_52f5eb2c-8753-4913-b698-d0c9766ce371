import RequestHelper from "../helpers/request.helper";
import axios, { AxiosInstance, AxiosResponse } from 'axios';

// Types and interfaces
interface HoneyCoinConfig {
  apiUrl: string;
  cryptoApiUrl: string;
  publicKey: string;
  apiKey: string;
}

interface TokenResponse {
  status: "success" | "error";
  token?: string;
  message?: string;
}

interface QuoteInfo {
  provider: string;
  providerQuoteId: string;
  from: string;
  providerId: number;
  to: string;
  fiatAmount: number;
  toAmount: number;
  cryptoAmount: number;
  fee: number;
  quoteId: string;
  expiresAt: string;
  quotedPrice: string;
}

interface CollectionPayload {
  amount: number;
  phoneNumber: string;
  currency: string;
  externalReference: string;
  momoOperatorId?: string;
  walletCurrency?: string;
}

interface PayoutPayload {
  amount: number;
  currency: string;
  country: string;
  externalReference: string;
  payoutMethod: {
    accountName: string;
    accountNumber: string;
    code: string;
    branchCode: string;
  };
  destination: string;
}

interface OffRampPayload {
  senderAmount: number;
  senderCurrency: string;
  receiverCurrency: string;
  chain: string;
  email: string;
  country: string;
  destination: string;
  payoutMethod: {
    country: string;
    destination: string;
    accountNumber: string;
    accountName: string;
  };
  externalReference: string;
}

class HoneyCoin {
  private config: HoneyCoinConfig;
  private requestHeaders: Record<string, string>;

  constructor() {
    this.config = {
      apiUrl: process.env.HONEYCOIN_API_URL ?? "",
      cryptoApiUrl: process.env.HONEYCOIN_CRYPTO_API_URL ?? "",
      publicKey: process.env.HONEYCOIN_PUBLIC_KEY ?? "",
      apiKey: process.env.HONEYCOIN_API_KEY ?? ""
    };

    this.requestHeaders = {
      "Accept": "application/json",
      "Content-Type": "application/json"
    };
  }

  
  // Generate authentication token for HoneyCoin API
  private async generateToken(): Promise<TokenResponse> {
    try {
      const endpoint = "/auth/generate-bearer-token";
      const data = {
        publicKey: this.config.publicKey,
        "api-key": this.config.apiKey,
      };

      axios.defaults.headers.common['api-key'] = this.config.apiKey;
      
      const response: AxiosResponse = await axios.post(
        `${this.config.apiUrl}${endpoint}`, 
        data
      );

      if (response?.data?.success) {
        return {
          status: "success",
          token: response.data.token
        };
      }

      return {
        status: "error",
        message: response?.data?.message || "Failed to generate token"
      };
    } catch (error: any) {
      console.error("Token generation error:", error);
      return {
        status: "error",
        message: error?.message ?? "Error generating token"
      };
    }
  }

  // Make request to HoneyCoin API
  private async makeRequest(
    method: "get" | "post" | "put",
    endpoint: string,
    data: any = {},
    useCryptoApi: boolean = false
  ): Promise<any> {
    try {
      const baseUrl = useCryptoApi ? this.config.cryptoApiUrl : this.config.apiUrl;
      const fullUrl = `${baseUrl}${endpoint}`.replace(/\s/g, "");
      
      await RequestHelper.setEndpoint(fullUrl);
      const tokenResponse = await this.generateToken();
      if (tokenResponse.status !== "success" || !tokenResponse.token) {
        throw new Error("Failed to generate Honeycoin token");
      }

      this.requestHeaders["Authorization"] = `Bearer ${tokenResponse.token}`;
      await RequestHelper.setHeaders(this.requestHeaders);
      
      if (data && Object.keys(data).length > 0) {
        await RequestHelper.setData(data);
      }

      await  RequestHelper[`${method}Request`]();
      const  errors = await RequestHelper.getErrors();
      const  responseData = await RequestHelper.getResults();
      return errors?.status ? responseData : responseData;
  
    } catch (error: any) {
      console.error(`Request error for ${method.toUpperCase()} ${endpoint}:`, error);
      throw new Error(error.message || "Request failed");
    }
  }

  //Generate unique reference for transactions
  private generateUniqueReference(): string {
    return `MUDA_PAYMENT${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
  }

 


  //Get list of supported countries
  public async getCountries(): Promise<any> {
    return await this.makeRequest("get", "/utilities/countries");
  }

  // Get list of banks for a specific country
  public async getBanks(country: string): Promise<any> {
    return await this.makeRequest("get", `/utilities/banks?country=${country}`);
  }

  /**
   * Get bank branches for a specific bank
   */
  public async getBankBranch(bankId: string): Promise<any> {
    return await this.makeRequest("get", `/utilities/branches?bankId=${bankId}`);
  }

  // Get mobile money providers for a currency
  public async getMobileMoneyProviders(currency: string = ""): Promise<any> {
    return await this.makeRequest("get", `/utilities/momo/providers?currency=${currency}`);
  }

  


  // Get all users
  public async getUsers(): Promise<any> {
    return await this.makeRequest("get", "/users");
  }

  // Get all transactions
  public async getTransactions(): Promise<any> {
    const response = await this.makeRequest("get", "/transactions");
    return response.data;
  }

  // Get a specific transaction by ID
  public async getTransaction(transactionId: string): Promise<any> {
    const response =  await this.makeRequest("get", `/transactions/${transactionId}`);
    return response.data;
  }

  
  // Create a mobile money collection
  public async createCollection(
    amount: number,
    phoneNumber: string,
    currency: string,
    externalReference: string,
    momoOperatorId: string = ""
  ): Promise<any> {
    const payload: CollectionPayload = {
      amount,
      phoneNumber,
      currency,
      externalReference,
      momoOperatorId
    };
    const response = await this.makeRequest("post", "/fiat/deposit/momo", payload);
    console.log("🔁 Collection request finished 3 >>> ", response);
    return response;
  }

  // Verify collection with OTP
  public async verifyCollection(transactionId: string): Promise<any> {
    return await this.makeRequest("post", `/fiat/deposit/${transactionId}/validate-otp`);
  }

  // Create a payout transaction
  public async createPayout(
    amount: number,
    currency: string,
    country: string,
    externalReference: string,
    accountName: string,
    accountNumber: string,
    code: string,
    destination: string,
    paymentType: string,
    branchCode: string
  ): Promise<any> {

    const payoutMethod: any = {
      accountName,
      accountNumber
    };

    if (paymentType === "bank") {
      payoutMethod.branchCode = branchCode;
    }
    if (currency !== "KES" && code !== "") {
      payoutMethod.code = code;
    }
    const payload: PayoutPayload = {
      amount,
      currency,
      country,
      externalReference,
      payoutMethod,
      destination
    };
    
    const response = await this.makeRequest("post", "/fiat/payout", payload);
    return response;
  }


  // Get foreign exchange rate
  public async getFXRate(
    fromCurrency: string, 
    toCurrency: string = "UGX", 
    amount: number
  ): Promise<any> {
    return await this.makeRequest(
      "get", 
      `/utilities/rates?from=${fromCurrency}&to=${toCurrency}&amount=${amount}`
    );
  }

  // Get off-ramp exchange rate
  public async getOffRampRate(
    fromCurrency: string, 
    toCurrency: string = "UGX", 
    amount: number
  ): Promise<QuoteInfo | null> {
    try {
      const response = await this.makeRequest(
        "get", 
        `/utilities/rates?from=${fromCurrency}&to=${toCurrency}&amount=${amount}`
      );

      if (!response?.success) {
        return null;
      }

      const data = response.data;
      return {
        provider: "honeycoin",
        providerQuoteId: data?.id || "",
        from: fromCurrency,
        providerId: 2,
        to: toCurrency,
        fiatAmount: data?.convertedAmount || 0,
        toAmount: data?.convertedAmount || 0,
        cryptoAmount: data?.cryptoAmount ?? amount,
        fee: data?.fee || 0,
        quoteId: data?.id || "",
        expiresAt: "",
        quotedPrice: data?.conversionRate || ""
      };
    } catch (error) {
      console.error("Error getting off-ramp rate:", error);
      return null;
    }
  }

 
  // Execute off-ramp transaction
  public async executeOffRamp(
    senderAmount: number,
    senderCurrency: string,
    receiverCurrency: string = "UGX",
    chain: string,
    email: string,
    country: string,
    destination: string,
    externalReference: string,
    payoutMtdCountry: string,
    payoutMtdProviderId: string,
    payoutMtdAccountNumber: string,
    payoutMtdAccountName: string
  ): Promise<any> {
    const payload: OffRampPayload = {
      senderAmount,
      senderCurrency,
      receiverCurrency,
      chain,
      email,
      country,
      destination,
      payoutMethod: {
        country: payoutMtdCountry,
        destination: payoutMtdProviderId,
        accountNumber: payoutMtdAccountNumber,
        accountName: payoutMtdAccountName
      },
      externalReference: externalReference || this.generateUniqueReference()
    };
    console.log("Off-ramp payload:", payload);
    return await this.makeRequest("post", "/minting/offramp", payload, true);
  }

  // Simulate payment webhook
  public async simulatePaymentWebhook(webhookStatus: string): Promise<any> {
    return await this.makeRequest("post", "/utilities/simulate-webhook", { webhookStatus });
  }

  // Get all webhooks
  public async getWebhooks(): Promise<any> {
    return await this.makeRequest("get", "/webhooks");
  }

  // Get specific webhook by ID
  public async getWebhook(webhookId: string): Promise<any> {
    return await this.makeRequest("get", `/webhook/${webhookId}`);
  }

  // Resend webhook by ID
  public async resendWebhook(webhookId: string): Promise<any> {
    return await this.makeRequest("post", `/fiat/payoutwebhooks/${webhookId}/resend`);
  }

  public async uniqueReference() {
    return `MUDA_PAYMENT${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
  }
}

export default new HoneyCoin();
