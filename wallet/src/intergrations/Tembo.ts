import axios, { AxiosResponse } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import dotenv from 'dotenv';
import logger from '../helpers/logger';
import Model, { Steps } from '../helpers/model';

dotenv.config();

// Tembo API Response Interfaces
interface TemboBaseResponse {
  success?: boolean;
  message?: string;
  statusCode?: string | number;
  reason?: string;
}

interface TemboVirtualAccountResponse extends TemboBaseResponse {
  id?: string;
  accountName?: string;
  accountNo?: string;
  reference?: string;
  tag?: string;
}

interface TemboBalanceResponse extends TemboBaseResponse {
  result?: {
    accountNo: string;
    accountName: string;
    branchCode: string;
    availableBalance: number;
    bookedBalance: number;
  };
}

interface TemboStatementResponse extends TemboBaseResponse {
  result?: {
    accountNo: string;
    statement: Array<{
      id: string;
      transactionId: string;
      reference: string;
      transactionType: string;
      channel: string;
      transactionDate: string;
      postingDate: string;
      valueDate: string;
      narration: string;
      currency: string;
      amountCredit: number;
      amountDebit: number;
      clearedBalance: number;
      bookedBalance: number;
    }>;
  };
}

interface TemboCollectionResponse extends TemboBaseResponse {
  transactionRef?: string;
  transactionId?: string;
}

interface TemboPaymentResponse extends TemboBaseResponse {
  transactionRef?: string;
  transactionId?: string;
}

// Webhook payload interface
interface TemboWebhookPayload {
  accountNo: string;
  payerName?: string;
  id: string;
  transactionId: string;
  reference: string;
  transactionType: string;
  channel: string;
  transactionDate: string;
  postingDate: string;
  valueDate: string;
  narration: string;
  currency: string;
  amountCredit: number;
  amountDebit: number;
  clearedBalance: number;
  bookedBalance: number;
}

// Standard response format following existing patterns
interface StandardResponse {
  status: number;
  message?: string;
  trans_id?: string;
  amount_transfered?: number;
  transaction_fee?: number;
  data?: any;
}

/**
 * TemboService - Comprehensive wrapper for Tembo API endpoints
 * Follows existing integration patterns from PegPay, MyFX, Quidax
 */
export default class TemboService extends Model {
  private baseURL: string;
  private authToken: string;
  private accountId: string;
  private secretKey: string;
  private webhookSecret: string;

  constructor() {
    super();

    // Define required environment variables with their descriptions
    const requiredEnvVars = [
      { key: 'TEMBO_AUTH_TOKEN', description: 'Tembo API authentication token' },
      { key: 'TEMBO_ACCOUNT_ID', description: 'Tembo account identifier' },
      { key: 'TEMBO_SECRET_KEY', description: 'Tembo API secret key' },
      { key: 'TEMBO_WEBHOOK_SECRET', description: 'Tembo webhook signature secret' },
      { key: 'WEBHOOK_BASE_URL', description: 'Base URL for webhook callbacks' },
      { key: 'TEMBO_COLLECTION_ACCOUNT', description: 'Muda collection account for receiving payments' }
    ];

    // Validate all required environment variables
    this.validateRequiredEnvVars(requiredEnvVars);

    // Set configuration values
    this.baseURL = process.env.TEMBO_API_URL || 'https://sandbox.temboplus.com';
    this.authToken = process.env.TEMBO_AUTH_TOKEN!;
    this.accountId = process.env.TEMBO_ACCOUNT_ID!;
    this.secretKey = process.env.TEMBO_SECRET_KEY!;
    this.webhookSecret = process.env.TEMBO_WEBHOOK_SECRET!;
  }

  /**
   * Validate required environment variables
   * @param requiredVars Array of required environment variable configurations
   */
  private validateRequiredEnvVars(requiredVars: Array<{ key: string; description: string }>): void {
    const missingVars: string[] = [];

    for (const envVar of requiredVars) {
      if (!process.env[envVar.key]) {
        missingVars.push(`${envVar.key} (${envVar.description})`);
      }
    }

    if (missingVars.length > 0) {
      throw new Error(
        `Missing required environment variables for Tembo integration:\n` +
        missingVars.map(varInfo => `  - ${varInfo}`).join('\n') +
        `\n\nPlease configure these environment variables before using TemboService.`
      );
    }
  }

  /**
   * Generate request headers following Tembo API requirements
   * Reuses pattern from Quidax integration
   */
  private getHeaders(includeAuth: boolean = true): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'x-request-id': uuidv4()
    };

    if (includeAuth) {
      if (this.authToken) {
        headers['Authorization'] = `Bearer ${this.authToken}`;
      }
      if (this.accountId) {
        headers['x-account-id'] = this.accountId;
      }
      if (this.secretKey) {
        headers['x-secret-key'] = this.secretKey;
      }
    }

    return headers;
  }

  /**
   * Make HTTP request with error handling
   * Follows pattern from existing integrations
   */
  private async makeRequest(
    method: 'GET' | 'POST',
    endpoint: string,
    data?: any,
    useCollectionAuth: boolean = false
  ): Promise<any> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const headers = useCollectionAuth ? 
        this.getCollectionHeaders() : 
        this.getHeaders();

      logger.info(`🔹 Tembo ${method} Request`, { url, data });

      let response: AxiosResponse;
      
      if (method === 'POST') {
        response = await axios.post(url, data, { headers });
      } else {
        response = await axios.get(url, { headers });
      }

      logger.info(`✅ Tembo Response`, { 
        status: response.status, 
        data: response.data 
      });

      return response.data;
    } catch (error: any) {
      logger.error(`❌ Tembo API Error`, {
        endpoint,
        error: error.response?.data || error.message,
        status: error.response?.status
      });

      // Return standardized error response
      return {
        success: false,
        statusCode: error.response?.status || 500,
        message: error.response?.data?.message || error.message,
        data: error.response?.data
      };
    }
  }

  /**
   * Get collection-specific headers (different auth pattern)
   */
  private getCollectionHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'x-account-id': this.accountId,
      'x-secret-key': this.secretKey,
      'x-request-id': uuidv4()
    };
  }

  /**
   * Normalize response to standard format
   * Follows pattern from ThirdPartyHandler
   */
  private normalizeResponse(
    temboResponse: any, 
    transactionId?: string,
    amount?: number
  ): StandardResponse {
    // Success cases
    if (temboResponse.success || 
        temboResponse.statusCode === 'PENDING_ACK' || 
        temboResponse.statusCode === 'PAYMENT_ACCEPTED' ||
        temboResponse.statusCode === 201) {
      return {
        status: 200,
        message: temboResponse.message || 'Transaction successful',
        trans_id: temboResponse.transactionRef || temboResponse.transactionId || transactionId,
        amount_transfered: amount,
        data: temboResponse
      };
    }

    // Error cases
    return {
      status: typeof temboResponse.statusCode === 'number' ? 
        temboResponse.statusCode : 500,
      message: temboResponse.message || temboResponse.reason || 'Transaction failed',
      data: temboResponse
    };
  }

  /**
   * Log transaction to database
   * Reuses pattern from existing integrations
   */
  private async logTransaction(
    transId: string,
    step: Steps,
    status: number,
    message: string,
    data: any
  ): Promise<void> {
    try {
      await this.saveTransactionLog(transId, "TEMBO", step, status, message, data);
    } catch (error) {
      logger.error('Failed to log transaction', { transId, error });
    }
  }

  /**
   * Detect mobile network from phone number
   * Follows pattern from PegPay integration
   */
  private detectMobileNetwork(phone: string): string {
    const cleanPhone = phone.replace(/\+/g, '');
    
    // Tanzania mobile prefixes
    if (cleanPhone.startsWith('25575') || cleanPhone.startsWith('25576') || cleanPhone.startsWith('25577')) {
      return 'TZ-VODACOM-C2B'; // M-Pesa
    }
    if (cleanPhone.startsWith('25578') || cleanPhone.startsWith('25579')) {
      return 'TZ-AIRTEL-C2B';
    }
    if (cleanPhone.startsWith('25571') || cleanPhone.startsWith('25572')) {
      return 'TZ-TIGO-C2B';
    }
    
    // Default to Tigo for unknown numbers
    return 'TZ-TIGO-C2B';
  }

  /**
   * Get payout network code
   */
  private getPayoutNetwork(phone: string): string {
    const cleanPhone = phone.replace(/\+/g, '');

    if (cleanPhone.startsWith('25575') || cleanPhone.startsWith('25576') || cleanPhone.startsWith('25577')) {
      return 'TZ-VODACOM-B2C';
    }
    if (cleanPhone.startsWith('25578') || cleanPhone.startsWith('25579')) {
      return 'TZ-AIRTEL-B2C';
    }
    if (cleanPhone.startsWith('25571') || cleanPhone.startsWith('25572')) {
      return 'TZ-TIGO-B2C';
    }

    return 'TZ-TIGO-B2C';
  }

  // ==================== VIRTUAL ACCOUNT METHODS ====================

  /**
   * Create Virtual Account for merchant
   * Maps to: POST /account
   */
  async createVirtualAccount(
    merchantId: string,
    metadata: { companyName: string; tag?: string }
  ): Promise<StandardResponse> {
    const transId = `VA_${merchantId}_${Date.now()}`;

    try {
      await this.logTransaction(transId, Steps.SEND_TO_THIRD_PARTY, 202, "Creating virtual account", metadata);

      const payload = {
        companyName: metadata.companyName,
        reference: `MUDA_${merchantId}_${Date.now()}`,
        tag: metadata.tag || process.env.TEMBO_DEFAULT_TAG || 'MUDA'
      };

      const response: TemboVirtualAccountResponse = await this.makeRequest(
        'POST',
        '/account',
        payload
      );

      const standardResponse = this.normalizeResponse(response, transId);

      await this.logTransaction(
        transId,
        Steps.THIRDPARTY_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Virtual account response',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(transId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  /**
   * Get Virtual Account Balance
   * Maps to: POST /account/balance
   */
  async getVirtualAccountBalance(accountNo: string): Promise<StandardResponse> {
    const transId = `BAL_${accountNo}_${Date.now()}`;

    try {
      await this.logTransaction(transId, Steps.SEND_TO_THIRD_PARTY, 202, "Getting account balance", { accountNo });

      const payload = { accountNo };
      const response: TemboBalanceResponse = await this.makeRequest(
        'POST',
        '/account/balance',
        payload
      );

      const standardResponse = this.normalizeResponse(response, transId);

      await this.logTransaction(
        transId,
        Steps.THIRDPARTY_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Balance retrieved',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(transId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  /**
   * Get Virtual Account Statement
   * Maps to: POST /account/statement
   */
  async getVirtualAccountStatement(
    accountNo: string,
    startDate: string,
    endDate: string
  ): Promise<StandardResponse> {
    const transId = `STMT_${accountNo}_${Date.now()}`;

    try {
      await this.logTransaction(transId, Steps.SEND_TO_THIRD_PARTY, 202, "Getting account statement", {
        accountNo, startDate, endDate
      });

      const payload = { accountNo, startDate, endDate };
      const response: TemboStatementResponse = await this.makeRequest(
        'POST',
        '/account/statement',
        payload
      );

      const standardResponse = this.normalizeResponse(response, transId);

      await this.logTransaction(
        transId,
        Steps.THIRDPARTY_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Statement retrieved',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(transId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  // ==================== COLLECTION METHODS ====================

  /**
   * Mobile Money Pull Request (Collection)
   * Maps to: POST /tembo/v1/collection
   * Follows pattern from PegPay.makeMMPullRequest
   *
   * CRITICAL: Collections go to Muda's collection account, NOT individual merchant accounts.
   * After webhook is received, funds must be transferred to merchant virtual account.
   */
  async makeMMPullRequest(
    userId: string,
    VendorTranId: string,
    TranAmount: string,
    phone: string
  ): Promise<StandardResponse> {
    try {
      const amount = parseFloat(TranAmount);
      const currency = 'TZS'; // Default to Tanzanian Shilling

      await this.logTransaction(VendorTranId, Steps.SEND_TO_THIRD_PARTY, 202, "Initiating mobile money collection", {
        phone, amount, currency, userId, note: "Collection goes to main Tembo account, tracked by transactionRef"
      });

      // Auto-detect network from phone number (following PegPay pattern)
      const channel = this.detectMobileNetwork(phone);
      const cleanPhone = phone.replace(/\+/g, '');

      const payload = {
        msisdn: cleanPhone,
        channel: channel,
        amount: amount,
        narration: `Collection for ${VendorTranId}`,
        // CRITICAL: This transactionRef is how we track which merchant the funds belong to
        transactionRef: VendorTranId,  // ← This maps back to transactions table client_id
        transactionDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
        callbackUrl: `${process.env.WEBHOOK_BASE_URL}/webhook/tembo`
      };

      const response: TemboCollectionResponse = await this.makeRequest(
        'POST',
        '/tembo/v1/collection',
        payload,
        true // Use collection auth headers
      );

      const standardResponse = this.normalizeResponse(response, VendorTranId, amount);

      await this.logTransaction(
        VendorTranId,
        Steps.THIRDPARTY_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Collection initiated - funds will go to main Tembo account',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(VendorTranId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }


  // ==================== PAYOUT METHODS ====================

  /**
   * Transfer funds from collection account to merchant virtual account
   * Maps to: POST /tembo/v1/transaction/transfer
   *
   * CRITICAL: This is called after webhook confirms collection was received.
   * Transfers funds from Muda's collection account to specific merchant virtual account.
   */
  async transferToMerchantAccount(
    transactionId: string,
    merchantAccountNo: string,
    amount: number,
    narration: string
  ): Promise<StandardResponse> {
    try {
      await this.logTransaction(transactionId, Steps.SEND_TO_THIRD_PARTY, 202, "Transferring funds to merchant virtual account", {
        merchantAccountNo, amount, narration
      });

      const payload = {
        amount: amount,
        fromAccountNo: process.env.TEMBO_COLLECTION_ACCOUNT, // Muda's collection account
        toAccountNo: merchantAccountNo, // Merchant's virtual account
        transactionDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
        narration: narration,
        externalRefNo: transactionId
      };

      const response = await this.makeRequest(
        'POST',
        '/tembo/v1/transaction/transfer',
        payload,
        true
      );

      const standardResponse = this.normalizeResponse(response, transactionId, amount);

      await this.logTransaction(
        transactionId,
        Steps.THIRDPARTY_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Funds transferred to merchant account',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(transactionId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  /**
   * Mobile Money Push Request (Payout)
   * Maps to: POST /tembo/v1/payment/wallet-to-mobile
   * Follows pattern from PegPay.makeMMPushRequest
   *
   * CRITICAL: Payouts are made FROM merchant virtual accounts, not main account.
   */
  async makeMMPushRequest(
    userId: string,
    VendorTranId: string,
    TranAmount: string,
    phone: string
  ): Promise<StandardResponse> {
    try {
      const amount = parseFloat(TranAmount);
      const currency = 'TZS'; // Default to Tanzanian Shilling

      // CRITICAL: Get merchant's virtual account number from userId
      // In production, this should be retrieved from database based on userId
      const merchantAccountNo = await this.getMerchantAccountNumber(userId);

      await this.logTransaction(VendorTranId, Steps.SEND_TO_THIRD_PARTY, 202, "Initiating mobile money payout from merchant account", {
        phone, amount, currency, merchantAccountNo, userId
      });

      // Auto-detect network for payout
      const serviceCode = this.getPayoutNetwork(phone);
      const cleanPhone = phone.replace(/\+/g, '');

      const payload = {
        countryCode: 'TZ',
        accountNo: merchantAccountNo, // Use merchant's virtual account
        serviceCode: serviceCode,
        amount: amount,
        msisdn: cleanPhone,
        narration: `Payout for ${VendorTranId}`,
        currencyCode: currency,
        recipientNames: 'Customer', // Default recipient name
        transactionRef: VendorTranId,
        transactionDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
        callbackUrl: `${process.env.WEBHOOK_BASE_URL}/webhook/tembo`
      };

      const response: TemboPaymentResponse = await this.makeRequest(
        'POST',
        '/tembo/v1/payment/wallet-to-mobile',
        payload,
        true
      );

      const standardResponse = this.normalizeResponse(response, VendorTranId, amount);

      await this.logTransaction(
        VendorTranId,
        Steps.THIRDPARTY_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Payout initiated from merchant account',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(VendorTranId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  /**
   * Bank Transfer from Merchant Virtual Account
   * Maps to: POST /tembo/v1/payment/wallet-to-mobile (with bank service code)
   *
   * CRITICAL: Transfers from merchant virtual account to bank accounts
   */
  async payToBank(
    userId: string,
    VendorTranId: string,
    TranAmount: string,
    bankCode: string,
    accountNumber: string,
    recipientName: string
  ): Promise<StandardResponse> {
    try {
      const amount = parseFloat(TranAmount);
      const currency = 'TZS';

      // Get merchant's virtual account number
      const merchantAccountNo = await this.getMerchantAccountNumber(userId);

      await this.logTransaction(VendorTranId, Steps.SEND_TO_THIRD_PARTY, 202, "Initiating bank transfer from merchant account", {
        bankCode, accountNumber, amount, merchantAccountNo, userId
      });

      const payload = {
        countryCode: 'TZ',
        accountNo: merchantAccountNo, // Use merchant's virtual account
        serviceCode: 'TZ-BANK-B2C', // Bank transfer service code
        amount: amount,
        msisdn: `${bankCode}:${accountNumber}`, // Bank format: BIC:ACCOUNT_NUMBER
        narration: `Bank transfer for ${VendorTranId}`,
        currencyCode: currency,
        recipientNames: recipientName,
        transactionRef: VendorTranId,
        transactionDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
        callbackUrl: `${process.env.WEBHOOK_BASE_URL}/webhook/tembo`
      };

      const response: TemboPaymentResponse = await this.makeRequest(
        'POST',
        '/tembo/v1/payment/wallet-to-mobile', // Same endpoint, different service code
        payload,
        true
      );

      const standardResponse = this.normalizeResponse(response, VendorTranId, amount);

      await this.logTransaction(
        VendorTranId,
        Steps.THIRDPARTY_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Bank transfer initiated from merchant account',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(VendorTranId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  /**
   * Get merchant's virtual account number
   * This should be implemented to retrieve from database
   */
  private async getMerchantAccountNumber(userId: string): Promise<string> {
    // TODO: Implement database lookup for merchant's virtual account
    // For now, return a placeholder or throw error

    // In production, this would be:
    // const merchant = await this.selectDataQuery('merchants', `user_id='${userId}'`);
    // return merchant[0].tembo_account_no;

    // For testing, use environment variable or throw error
    const accountNo = process.env[`TEMBO_MERCHANT_ACCOUNT_${userId}`];
    if (!accountNo) {
      throw new Error(`No Tembo virtual account found for merchant: ${userId}. Please create virtual account first.`);
    }

    return accountNo;
  }

  // ==================== BALANCE & TRANSACTION METHODS ====================

  /**
   * Get Collection Balance
   * Maps to: POST /tembo/v1/wallet/collection-balance
   * Based on actual Tembo documentation
   */
  async getCollectionBalance(): Promise<StandardResponse> {
    const transId = `COL_BAL_${Date.now()}`;

    try {
      await this.logTransaction(transId, Steps.SEND_TO_THIRD_PARTY, 202, "Getting collection balance", {});

      // Use the correct Tembo endpoint for collection balance
      const response = await this.makeRequest(
        'POST',
        '/tembo/v1/wallet/collection-balance',
        {}, // Empty body as per Tembo docs
        true  // Use collection auth headers
      );

      const standardResponse = this.normalizeResponse(response, transId);

      await this.logTransaction(
        transId,
        Steps.THIRDPARTY_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Collection balance retrieved',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(transId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  /**
   * Get Transaction Status
   * Maps to: GET /tembo/v1/payment/status/{transactionId} or similar
   */
  async getTransactionStatus(transactionRef: string): Promise<StandardResponse> {
    const transId = `STATUS_${transactionRef}_${Date.now()}`;

    try {
      await this.logTransaction(transId, Steps.TRANS_STATUS_CHECK, 202, "Checking transaction status", { transactionRef });

      // Note: Exact endpoint may vary - check Tembo docs
      const response = await this.makeRequest(
        'GET',
        `/tembo/v1/payment/status/${transactionRef}`,
        undefined,
        true
      );

      const standardResponse = this.normalizeResponse(response, transId);

      await this.logTransaction(
        transId,
        Steps.TRANS_STATUS_RESPONSE,
        standardResponse.status,
        standardResponse.message || 'Status retrieved',
        response
      );

      return standardResponse;
    } catch (error: any) {
      await this.logTransaction(transId, Steps.ERROR_REPONSE, 500, error.message, error);
      throw error;
    }
  }

  // ==================== WEBHOOK METHODS ====================

  /**
   * Verify webhook signature
   * Follows Tembo's HMAC SHA-256 signature verification
   */
  verifyWebhookSignature(
    payload: TemboWebhookPayload,
    timestamp: string,
    receivedSignature: string
  ): boolean {
    try {
      if (!this.webhookSecret) {
        logger.warn('Webhook secret not configured');
        return false;
      }

      // Decode the secret from base64
      const secret = Buffer.from(this.webhookSecret, 'base64');

      // Reconstruct the concatenated string as per Tembo docs
      const concatenatedString =
        timestamp +
        payload.accountNo +
        payload.id +
        payload.transactionId +
        payload.reference +
        payload.transactionType +
        payload.channel +
        payload.transactionDate +
        payload.postingDate +
        payload.valueDate +
        payload.narration +
        payload.currency +
        Math.trunc(payload.amountCredit).toString() +
        Math.trunc(payload.amountDebit).toString() +
        Math.trunc(payload.clearedBalance).toString() +
        Math.trunc(payload.bookedBalance).toString();

      // Compute HMAC signature
      const hmac = crypto.createHmac('sha256', secret);
      hmac.update(Buffer.from(concatenatedString, 'utf-8'));
      const computedSignature = hmac.digest('base64');

      return computedSignature === receivedSignature;
    } catch (error: any) {
      logger.error('Webhook signature verification failed', { error: error.message });
      return false;
    }
  }

  /**
   * Process webhook payload
   * Normalizes webhook data to standard format
   *
   * CRITICAL: This webhook indicates funds have been received in Tembo's main collection account.
   * The payload.reference contains the transactionRef that maps back to the merchant's transaction.
   */
  async processWebhook(
    payload: TemboWebhookPayload,
    signature: string,
    timestamp: string
  ): Promise<StandardResponse> {
    try {
      // Verify signature
      const isValid = this.verifyWebhookSignature(payload, timestamp, signature);
      if (!isValid) {
        return {
          status: 401,
          message: 'Invalid webhook signature',
          data: payload
        };
      }

      await this.logTransaction(
        payload.reference,
        Steps.WEBHOOK_RECEIVED,
        200,
        'Tembo webhook received - funds in main collection account',
        payload
      );

      // Determine transaction type and amount based on Tembo webhook structure
      // Both amountCredit and amountDebit can be present in the same transaction
      const creditAmount = payload.amountCredit || 0;
      const debitAmount = payload.amountDebit || 0;

      // Determine the primary transaction direction
      let transactionDirection: 'CREDIT' | 'DEBIT' | 'TRANSFER';
      let primaryAmount: number;

      if (creditAmount > 0 && debitAmount === 0) {
        // Pure credit transaction (money coming in)
        transactionDirection = 'CREDIT';
        primaryAmount = creditAmount;
      } else if (debitAmount > 0 && creditAmount === 0) {
        // Pure debit transaction (money going out)
        transactionDirection = 'DEBIT';
        primaryAmount = debitAmount;
      } else if (creditAmount > 0 && debitAmount > 0) {
        // Transfer or transaction with fees
        transactionDirection = 'TRANSFER';
        primaryAmount = Math.max(creditAmount, debitAmount); // Use larger amount
      } else {
        // Both are zero - unusual case
        transactionDirection = 'CREDIT';
        primaryAmount = 0;
      }

      // Use Tembo's actual transaction type and channel for more context
      const temboTransactionType = payload.transactionType; // e.g., "H4"
      const channel = payload.channel; // e.g., "CMM" for mobile money

      // CRITICAL: For collections (credits), this means a customer paid into Tembo's main account
      // The payload.reference is the transactionRef we sent, which maps to our transactions table
      // This allows us to identify which merchant the funds belong to

      if (transactionDirection === 'CREDIT') {
        logger.info(`💰 Collection received in Tembo main account`, {
          reference: payload.reference,
          creditAmount: creditAmount,
          debitAmount: debitAmount,
          primaryAmount: primaryAmount,
          channel: payload.channel,
          payer: payload.payerName,
          temboTransactionType: temboTransactionType,
          note: 'Funds need to be attributed to merchant based on reference'
        });
      }

      // Return standardized webhook data that follows existing webhook patterns
      return {
        status: 200,
        message: 'Tembo webhook processed - merchant attribution via reference',
        trans_id: payload.reference,  // ← This maps back to transactions.trans_id
        amount_transfered: primaryAmount,
        data: {
          ...payload,
          transaction_direction: transactionDirection,
          primary_amount: primaryAmount,
          credit_amount: creditAmount,
          debit_amount: debitAmount,
          tembo_transaction_type: temboTransactionType,
          channel: channel,
          is_collection: transactionDirection === 'CREDIT',
          merchant_attribution_note: 'Use payload.reference to find merchant in transactions table',
          processed_at: new Date().toISOString()
        }
      };
    } catch (error: any) {
      logger.error('Tembo webhook processing failed', { error: error.message, payload });
      return {
        status: 500,
        message: 'Tembo webhook processing failed',
        data: { error: error.message, payload }
      };
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Validate phone number format for Tanzania
   * Follows pattern from PegPay integration
   */
  validatePhoneNumber(phone: string): { valid: boolean; message?: string } {
    const cleanPhone = phone.replace(/[\s\-\+]/g, '');

    // Tanzania phone numbers should start with 255 and be 12 digits
    if (!cleanPhone.startsWith('255')) {
      return { valid: false, message: 'Phone number must start with 255 (Tanzania country code)' };
    }

    if (cleanPhone.length !== 12) {
      return { valid: false, message: 'Phone number must be 12 digits including country code' };
    }

    // Check if it matches known network prefixes
    const network = this.detectMobileNetwork(cleanPhone);
    if (!network.includes('TZ-')) {
      return { valid: false, message: 'Unsupported mobile network' };
    }

    return { valid: true };
  }

  /**
   * Format amount for Tembo API
   */
  formatAmount(amount: number): number {
    // Tembo expects amounts in minor units (e.g., cents for TZS)
    return Math.round(amount * 100) / 100;
  }

  /**
   * Get supported currencies
   */
  getSupportedCurrencies(): string[] {
    return ['TZS']; // Currently only Tanzanian Shilling
  }

  /**
   * Get supported mobile networks
   */
  getSupportedNetworks(): string[] {
    return [
      'TZ-TIGO-C2B', 'TZ-TIGO-B2C',
      'TZ-AIRTEL-C2B', 'TZ-AIRTEL-B2C',
      'TZ-VODACOM-C2B', 'TZ-VODACOM-B2C'
    ];
  }
}
