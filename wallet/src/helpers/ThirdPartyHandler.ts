import PegaPay from "../intergrations/PegPay";
import <PERSON><PERSON>oin from "../intergrations/HoneyCoin";
import { post, get } from "./httpRequest";
import Model, { Steps } from "./model";
import dotenv from "dotenv";
import logger from "./logger";
import MyFX from "../intergrations/MyFX";
import Quidax from "../intergrations/Quidax";
import LiquidityClient, { QuoteInfo } from "../intergrations/LR";
import { initiatePayout } from "../intergrations/Utilia";
import { WebhookData } from "./model";
// Import TemboService - following existing integration pattern
import TemboService from "../intergrations/Tembo";

dotenv.config();
interface ThirdPartyResponseBody {
    status: number;
    message?: string;
    trans_id?: string;
    amount_transfered?: number;
    transaction_fee?: number;
    data?: any;
}


/**
 * ThirdPartyHandler class for managing all third-party payment integrations
 * This class centralizes all third-party API calls and provides consistent logging
 */
class ThirdPartyHandler extends Model {
    async checkIpWhitelist(ip: string, clientId: string) {
        const internalIp = ["***********", "************"]
        if (internalIp.includes(ip)) {
            return true;
        }
        const whitelist = await this.selectDataQuery("ip_whitelist", `client_id = '${clientId}'`);
        if (whitelist.length === 0) {
            return true;
        }
        const whitelistIps = whitelist.map((item: any) => item.ip_address);
        console.log("whitelistIps", whitelistIps, ip, whitelistIps.includes(ip))
        return whitelistIps.includes(ip);
    }

    saveApiLog(body: any, ip: string) {
        const clientId = body.clientId || "NA"
        const userId = body.userId || "NA"
        this.insertData("api_logs", { client_id: clientId, user_id: userId, body: JSON.stringify(body), ip: ip })
        return true;
    }

    private pegaPay: PegaPay;
    private honeycoin: typeof HoneyCoin;
    // Add TemboService instance - following existing pattern
    private temboService: TemboService;

    constructor() {
        super();
        this.pegaPay = new PegaPay();
        this.honeycoin = HoneyCoin;
        // Initialize TemboService - following existing pattern
        this.temboService = new TemboService();
    }

    /**
     * Handles third-party integrations for payouts based on service type
     * @param data - Object containing transaction details
     * @returns Response object
     */
    async handlePayout(data: {
        trans_id: string;
        clientId: string;
        amount: string;
        phone: string;
        SessionId: string;
        service_name: string;
        currency: string;
        extra_data?: any;
        reference_id?: string;
    }) {
        try {
            console.log(`🔹 Processing payout via ${data.service_name} for transaction ID: ${data.trans_id}`);

            await this.saveTransactionLog(data.trans_id, "PAYOUT", Steps.SEND_TO_THIRD_PARTY, 202, "REQUEST SENT", data)
            let response: ThirdPartyResponseBody = {
                status: 500,
                message: "Transaction failed",
                data: {}
            };
            const userId = process.env.MUDA_PEG_USER || "";

            // Select payment provider based on service_name
            switch (data.service_name) {
                case "INTERAC":
                    // Implement Interac payout logic here
                    response = await this.processInteracPayout(userId, data, data.extra_data || {});
                    break;
                case "MOBILE_MONEY":
                    // Use PegaPay for mobile money
                    response = await this.processMobileMoneyPayout(userId, data);
                    break;

                case "BANK_TRANSFER":
                    // Implement bank transfer logic here
                    response = await this.processBankTransferPayout(userId, data);
                    break;

                case "CRYPTO_TRANSFER":
                    // Implement crypto payout logic here
                    response = await this.processCryptoPayout(data);
                    break;
                case "LIQUIDITY_RAIL":
                    response = await this.processCryptoPayout(data);
                    //    response = await this.processLRPayout(data);
                    break;

                case "MPESA_PAYOUT":
                      response = await this.processMPesaPayout(userId, data, data.extra_data || {});
                      //    response = await this.processLRPayout(data);
                      break;

                // Add Tembo service cases - following existing pattern
                case "TEMBO_MOBILE":
                case "TANZANIA_MOBILE":
                    response = await this.processTemboMobilePayout(data);
                    break;

                default:
                    // Return error for unknown service types
                    console.log(`❌ Unknown service type: ${data.service_name}`);
                    return this.makeResponse(400, `Unsupported payment service: ${data.service_name}`, {
                        trans_id: data.trans_id,
                        status: "FAILED"
                    });
            }



            const allowedStatus = ['PENDING', 'MINT_FAILED', 'failed', 'MINT_INITIATED', 'SUCCESS', 'INITIATED', 'RECEIVED', 'ONHOLD']

            const responseStatus = response.status
            const message = response.message || "Transaction failed"

            if (responseStatus == 200) {
                await this.updateTransaction(data.trans_id, "SUCCESS", message, response)
            } else if (responseStatus == 202) {
                await this.updateTransaction(data.trans_id, "PENDING", message, response)
            } else if (responseStatus == 400) {
                await this.updateTransaction(data.trans_id, "ONHOLD", message, response)
            } else {
                await this.updateTransaction(data.trans_id, "ONHOLD", message, response)
            }

            const trans_id = data.trans_id;
            const webhookData = await this.composeWebhookData(trans_id, responseStatus, response.message || "Transaction failed");
            // Send webhook notification
            this.saveWebhook(
                responseStatus,
                data.clientId,
                data.trans_id,
                "transaction_status",
                responseStatus.toString(),
                response.message || "Transaction failed"
            );
            // responseStatus
            const responseInfo = {
                status: responseStatus,
                message: response.message || "Transaction being processed",
                data: webhookData
            }
            return responseInfo;
            // Return standardized response

        } catch (error: any) {
            console.error(`❌ Error in handlePayout:`, error);

            return this.makeResponse(500, "Payment processing error", {
                error: error.message,
                trans_id: data.trans_id,
                status: "ONHOLD"
            });
        }
    }

    /**
     * Process MPESA Money Payout
     * @param userId PegPay user ID
     * @param data Transaction data
     * @returns Standardized response
     */
    private async processMPesaPayout(userId: string, data: any, extra_data: any) {
        try {

            const name = `${data.accountName}` || "Unknown User";
            const user_email = data.user_email;
            const amount = data.amount;
            const body = {
                name: name,
                user_email,
                amount
            };
            this.saveTransactionLog(data.trans_id, "PAYOUT", Steps.SEND_TO_THIRD_PARTY, 202, "REQUEST SENT", body)
            // const reference = await this.honeycoin.uniqueReference()
            const amountToPayout: number = Math.trunc(parseFloat(Number(data.amount).toString()));
            const response_payout: any = await this.honeycoin.createPayout( amountToPayout, data.currency, data.country, data.trans_id, data.accountName, data.accountNumber, data.code, data.destination, data.paymentType, data.branchCode);
            console.log(`processMPesaPayout`, response_payout)   
            const status = response_payout.status ? 200 : 500;
            this.saveTransactionLog(data.trans_id, "RESPONSE", Steps.THIRDPARTY_RESPONSE, status, "RESPONSE RECEIVED", response_payout)
            if (status === 200) {
                const response: ThirdPartyResponseBody = {
                    status: 200,
                    trans_id: response_payout.data.transaction_number,
                    amount_transfered: response_payout.data.amount_transfered,
                    transaction_fee: response_payout.data.transaction_fee,
                    message: "Honey coin payout completed successfully",
                    data: response_payout.data || {},
                }
                return response;
            } else {
                const response: ThirdPartyResponseBody = {
                    status: 500,
                    message: response_payout.error || "Honey coin payout failed",
                    data: response_payout.data || {},
                }
                return response;
            }

        } catch (error: any) {

            this.saveTransactionLog(data.trans_id, "ERROR", Steps.ERROR_REPONSE, 500, "THIRDPARTY ERROR RECEIVED", error)
            console.error(`❌ Error in processHoneyCoinPayout:`, error);
            return this.makeResponse(500, "Honey coin payout failed", {
                error: error.message,
                trans_id: data.trans_id,
                status: "ONHOLD"
            });
        }
    }


    async processInteracPayout(userId: string, data: any, extra_data: any) {
        try {

            const naame = extra_data.interact_name || "Unknown User";
            const interact_email = extra_data.interact_email
            const amount = data.amount;

            const body = {
                name: naame,
                interact_email,
                amount
            };

            this.saveTransactionLog(data.trans_id, "PAYOUT", Steps.SEND_TO_THIRD_PARTY, 202, "REQUEST SENT", body)
            const interacResponse = await MyFX.makePayout(naame, interact_email, amount);
            console.log(`processInteracPayout`, interacResponse)

            const status = interacResponse.success ? 200 : 500;
            this.saveTransactionLog(data.trans_id, "RESPONSE", Steps.THIRDPARTY_RESPONSE, status, "RESPONSE RECEIVED", interacResponse)

            if (status === 200) {
                const response: ThirdPartyResponseBody = {
                    status: 200,
                    trans_id: interacResponse.data.transaction_number,
                    amount_transfered: interacResponse.data.amount_transfered,
                    transaction_fee: interacResponse.data.transaction_fee,
                    message: "Interac payout completed successfully",
                    data: interacResponse.data || {},

                }
                return response;
            } else {
                const response: ThirdPartyResponseBody = {
                    status: 500,
                    message: interacResponse.error || "Interac payout failed",
                    data: interacResponse.data || {},
                }
                return response;
            }
        } catch (error: any) {
            this.saveTransactionLog(data.trans_id, "ERROR", Steps.ERROR_REPONSE, 500, "THIRDPARTY ERROR RECEIVED", error)
            console.error(`❌ Error in processInteracPayout:`, error);
            return this.makeResponse(500, "Interac payout failed", {
                error: error.message,
                trans_id: data.trans_id,
                status: "ONHOLD"
            });
        }
    }


    /**
     * Process Mobile Money Payout
     * @param userId PegPay user ID
     * @param data Transaction data
     * @returns Standardized response
     */
    private async processMobileMoneyPayout(userId: string, data: any) {
        const pegResponse: any = await this.pegaPay.makeMMPushRequest(
            userId,
            data.trans_id,
            data.amount,
            data.SessionId,
            data.phone
        );
        const error_message = pegResponse.description || "Transaction failed"

        console.log(`processMobileMoneyPayout`, pegResponse)


        // Standardize the response format
        let status = 500;
        let responseData: ThirdPartyResponseBody = {
            status: 500,
            message: "Transaction failed",
            data: {}
        };

        if (pegResponse && typeof pegResponse === 'object' && 'statusCode' in pegResponse) {
            if (pegResponse.statusCode === "0" && pegResponse.description === "SUCCESS") {
                status = 200;
                responseData = {
                    status: 200,
                    message: "Transaction completed successfully",
                    data: pegResponse || {}
                };
            } else if (pegResponse.statusCode === "122" && pegResponse.description === "PENDING") {
                status = 202;
                responseData = {
                    status: 202,
                    message: "Transaction is being processed by payment provider",
                    data: pegResponse || {}
                };
            } else {
                status = 500;
                responseData = {
                    status: 500,
                    message: pegResponse.description || "Transaction failed",
                    data: pegResponse || {}
                };
            }
        }

        return responseData;
    }

    /**
     * Process Bank Transfer Payout
     * @param userId User ID
     * @param data Transaction data
     * @returns Standardized response
     */
    private async processBankTransferPayout(userId: string, data: any) {
        // This is a placeholder for bank transfer implementation
        // In a real implementation, this would call a bank transfer API

        // Simulate a successful response
        return {
            status: 200,
            data: {
                status: "SUCCESS",
                message: "Bank transfer completed successfully"
            }
        };
    }

    private async processLRPayout(data: any) {
        console.log(`processLRPayout`, data)

        const asset = data.asset.toUpperCase();
        const amount = data.amount;
        const trans_id = data.extra_data;

        const lr = new LiquidityClient();
        const provider_id = data.extra_data.providerId;
        const service_id = data.extra_data.service_id;
        const quoteId = data.extra_data.quoteId;
        const payment_method_id = data.payment_method_id;

        const quoteInfo: QuoteInfo = {
            quote_id: quoteId,
            provider_id: provider_id,
            reference_id: trans_id,
            service_name: "BANK_TRANSFER",
            send_asset: asset,
            send_amount: amount,
            service_id: service_id,
            chain: "TRON",
            source: process.env.LR_SOURCE || "LR_PAYOUT",
            payment_method_id: payment_method_id
        };
        const quote = await lr.createQuote(quoteInfo);
        const status = quote.status;
        console.log(`processLRPayout`, quote)

        if (quote.status === 200) {
            const payoutAddress = quote.data.payout_address;
            const send_amount = quote.data.send_amount;
            const send_asset = quote.data.send_asset
            const memo = quote.data.memo
            const quote_id = quote.data.quote_id

            const payoutObj = {
                trans_id: trans_id,
                clientId: data.clientId,
                amount: send_amount,
                asset: send_asset,
                to_address: payoutAddress,
                memo: memo,
            }
            this.saveTransactionLog(data.trans_id, "PAYOUT", Steps.SEND_TO_THIRD_PARTY, 202, "REQUEST SENT", payoutObj)

            const processCryptoPayout: ThirdPartyResponseBody = await this.processCryptoPayout(payoutObj);
            this.saveTransactionLog(data.trans_id, "RESPONSE", Steps.THIRDPARTY_RESPONSE, 200, "RESPONSE RECEIVED", processCryptoPayout)
            return processCryptoPayout;
        } else {
            const response: ThirdPartyResponseBody = {
                status: 500,
                message: quote.message || "Transaction failed",
                data: quote || {}
            }
            return response;
        }
    }

    async updateTransactionExtReference(trans_id: string, transactionId: string) {
        try {
            return await this.updateData("transactions", `trans_id='${trans_id}'`, { ext_reference: transactionId })
        } catch (error: any) {
            console.error(`❌ Error in updateTransactionExtReference:`, error);
            return false;
        }
    }

    async processCryptoPayout(data: any) {
        console.log(`processCryptoPayout`, data)
        const asset = data.currency
        const amount = data.amount;
        const to_address = data.phone;
        const reference_id = data.reference_id;
        const trans_id = data.trans_id;
        const memo = data.memo;

        const assetInfo = await this.selectDataQuery("utilia_assets", `asset_code='${asset}'`);
        const asset_id = assetInfo[0].asset

        const transactionObject = {
            destination: to_address,
            asset: asset_id,
            amount: amount,
            memo: memo,
            payFeeFromAmount: false,
            validateOnly: false,
            note: 'payment',
            requestId: reference_id
        }

        this.saveTransactionLog(data.trans_id, "REQUEST", Steps.THIRDPARTY_REQUEST, 200, "REQUEST SENT", transactionObject)

        // withdraw from utila
        const utilaWithdraw = await initiatePayout(transactionObject)
        this.saveTransactionLog(data.trans_id, "RESPONSE", Steps.THIRDPARTY_RESPONSE, 200, "RESPONSE RECEIVED", utilaWithdraw)

        console.log(`processCryptoPayout`, utilaWithdraw)
        // send from quidax
        //  const response = await Quidax.createWithdraw('me', asset, amount,asset, asset, to_address, "", trans_id, "trc20");
        //  console.log(`processCryptoPayout`, response)

        const uData = utilaWithdraw.data
        if (uData) {
            const transName = uData.transaction.name
            const parts = transName.split('/');
            const transactionId = parts[parts.length - 1];
            await this.updateTransactionExtReference(trans_id, transactionId)

            const response: ThirdPartyResponseBody = {
                status: 202,
                message: "Transaction is pending approval",
                data: uData || {}
            }
            return response;
        } else {
            const response: ThirdPartyResponseBody = {
                status: 500,
                message: "Transaction failed",
                data: utilaWithdraw || {}
            }
            return response;
        }
    }

    /**
     * Handles third-party integrations for collections based on service type
     * @param data - Object containing transaction details
     * @returns Response object
     */
    async handleCollection(data: {
        trans_id: string;
        clientId: string;
        amount: string;
        phone: string;
        SessionId: string;
        service_name: string;
        currency: string;
        momoOperatorId?: string;
    }) {
        try {
            console.log(`🔹 Processing collection via ${data.service_name} for transaction ID: ${data.trans_id}`);

            this.saveTransactionLog(data.trans_id, "COLLECTION", Steps.SEND_TO_THIRD_PARTY, 202, "REQUEST SENT", data)


            let response;
            const userId = process.env.MUDA_PEG_USER || "";

            // Select payment provider based on service_name
            switch (data.service_name) {
                case "MOBILE_MONEY":
                    // Use PegaPay for mobile money collection
                    response = await this.processMobileMoneyCollection(userId, data);
                    break;

                case "BANK_TRANSFER":
                    // Implement bank transfer collection logic here
                    response = await this.processBankTransferCollection(userId, data);
                    break;

                case "CARD_PAYMENT":
                    // Implement card payment logic here
                    response = await this.processCardPaymentCollection(userId, data);
                    break;

                case "MPESA_COLLECTION":
                    response = await this.processMPesaCollection(userId, data);
                    break;

                default:
                    // Return error for unknown service types
                    console.log(`❌ Unknown service type: ${data.service_name}`);
                    return this.makeResponse(400, `Unsupported payment service: ${data.service_name}`, {
                        trans_id: data.trans_id,
                        status: "FAILED"
                    });
            }

            this.saveTransactionLog(data.trans_id, "COLLECTION", Steps.THIRDPARTY_RESPONSE, 200, "Collection processing success", response.data);
            await this.updateData("transactions", `trans_id='${data.trans_id}'`, {
                status: response.data.status
            });

            // Return standardized response
            return this.makeResponse(response.status, response.data.message, {
                trans_id: data.trans_id,
                status: response.data.status
            });

        } catch (error: any) {
            console.error(`❌ Error in handleCollection:`, error);
            this.saveTransactionLog(data.trans_id, "COLLECTION", Steps.ERROR_REPONSE, 500, "Collection processing error", error);
            return this.makeResponse(500, "Collection processing error", {
                error: error.message,
                trans_id: data.trans_id,
                status: "FAILED"
            });
        }
    }

    private async processMPesaCollection(userId: string, data: any) {
            const reference = await this.honeycoin.uniqueReference()
            console.log("🔹 Processing collection via MPESA for transaction ID: ", data.trans_id, reference, data)
            const amountToCollection: number = Math.trunc(parseFloat(Number(data.amount).toString())); 
            this.honeycoin.createCollection(
              amountToCollection,
              data.phone,
              data.currency,
              data.trans_id, // `${reference}`, collections id use reference
              data?.momoOperatorId
            )
            .then(response => {

                console.log("✅ Pull request successful:", response);
                this.saveTransactionLog(data.trans_id, "SENT", Steps.THIRDPARTY_RESPONSE, 200, "RESPONSE RECEIVED", response)
            })
            .catch(error => {
                
                console.log("🔁 Collection request finished 1", error);
                this.saveTransactionLog(data.trans_id, "NOT_SENT", Steps.THIRDPARTY_RESPONSE, 400, "RESPONSE RECEIVED", error)
            })
            .finally(() => {
                console.log("🔁 Collection request finished 2");
                this.saveTransactionLog(data.trans_id, "FAILED", Steps.THIRDPARTY_RESPONSE, 500, "RESPONSE RECEIVED", {})

            });

        // For collections, we typically get a PENDING status initially
        // The actual success/failure will be determined by webhook callbacks
        return {
            status: 202,
            data: {
                status: "PENDING",
                message: "Collection request sent to payment provider"
            }
        };
    }


    private async processMobileMoneyCollection(userId: string, data: any) {
        this.pegaPay.makeMMPullRequest(
            userId,
            data.trans_id,
            data.amount,
            data.SessionId,
            data.phone
        )
            .then(response => {
                console.log("✅ Pull request successful:", response);
                this.saveTransactionLog(data.trans_id, "SENT", Steps.THIRDPARTY_RESPONSE, 200, "RESPONSE RECEIVED", response)
            })
            .catch(error => {
                this.saveTransactionLog(data.trans_id, "NOT_SENT", Steps.THIRDPARTY_RESPONSE, 400, "RESPONSE RECEIVED", error)

            })
            .finally(() => {
                console.log("🔁 Pull request finished");
                this.saveTransactionLog(data.trans_id, "FAILED", Steps.THIRDPARTY_RESPONSE, 500, "RESPONSE RECEIVED", {})

            });


        // For collections, we typically get a PENDING status initially
        // The actual success/failure will be determined by webhook callbacks
        return {
            status: 202,
            data: {
                status: "PENDING",
                message: "Collection request sent to payment provider"
            }
        };
    }

    /**
     * Process Bank Transfer Collection
     * @param userId User ID
     * @param data Transaction data
     * @returns Standardized response
     */
    private async processBankTransferCollection(userId: string, data: any) {
        // This is a placeholder for bank transfer collection implementation
        // In a real implementation, this would call a bank transfer API

        // Simulate a pending response
        return {
            status: 202,
            data: {
                status: "INITIATED",
                message: "Bank transfer collection initiated"
            }
        };
    }

    /**
     * Process Card Payment Collection
     * @param userId User ID
     * @param data Transaction data
     * @returns Standardized response
     */
    private async processCardPaymentCollection(userId: string, data: any) {
        // This is a placeholder for card payment implementation
        // In a real implementation, this would call a card payment gateway API

        // Simulate a successful response
        return {
            status: 200,
            data: {
                status: "SUCCESS",
                message: "Card payment processed successfully"
            }
        };
    }

    /**
     * Handles third-party validation requests
     * @param data - Object containing validation details
     * @returns Response object
     */
    async handleValidation(data: {
        phone: string;
        service_name: string;
        currency: string;
    }) {
        try {
            console.log(`🔹 Processing validation via ${data.service_name} for phone: ${data.phone}`);


            let response;

            // Select validation method based on service_name
            switch (data.service_name) {
                case "MOBILE_MONEY":
                    // Use PegaPay for mobile money validation
                    response = await this.validateMobileMoneyAccount(data.phone);
                    break;

                case "BANK_TRANSFER":
                    // Implement bank account validation logic here
                    response = await this.validateBankAccount(data);
                    break;

                case "CARD_PAYMENT":
                    // Implement card validation logic here
                    response = await this.validateCardPayment(data);
                    break;

                default:
                    // Return error for unknown service types
                    console.log(`❌ Unknown service type: ${data.service_name}`);
                    return this.makeResponse(400, `Unsupported validation service: ${data.service_name}`, {
                        isValid: false,
                        message: `Service type '${data.service_name}' is not supported`
                    });
            }


            // Return standardized response
            return this.makeResponse(response.status, response.data.message, response.data);

        } catch (error: any) {
            console.error(`❌ Error in handleValidation:`, error);



            return this.makeResponse(500, "Validation error", {
                error: error.message,
                isValid: false
            });
        }
    }

    /**
     * Validate Mobile Money Account
     * @param phone Phone number to validate
     * @returns Standardized response
     */
    private async validateMobileMoneyAccount(phone: string) {
        const accountName = await this.pegaPay.validatePhoneNumber(phone);

        if (accountName && accountName !== "") {
            return {
                status: 200,
                data: {
                    isValid: true,
                    accountName: accountName,
                    message: "Account validated successfully"
                }
            };
        } else {
            return {
                status: 400,
                data: {
                    isValid: false,
                    accountName: "",
                    message: "Invalid account number"
                }
            };
        }
    }

    /**
     * Validate Bank Account
     * @param data Validation data
     * @returns Standardized response
     */
    private async validateBankAccount(data: any) {
        // This is a placeholder for bank account validation
        // In a real implementation, this would call a bank API

        // Simulate a successful validation
        return {
            status: 200,
            data: {
                isValid: true,
                accountName: "John Doe",
                accountNumber: data.phone,
                bankName: "Example Bank",
                message: "Bank account validated successfully"
            }
        };
    }

    /**
     * Validate Card Payment
     * @param data Validation data
     * @returns Standardized response
     */
    private async validateCardPayment(data: any) {
        // This is a placeholder for card validation
        // In a real implementation, this would call a card payment gateway API

        // Simulate a successful validation
        return {
            status: 200,
            data: {
                isValid: true,
                cardType: "Visa",
                lastFour: "1234",
                message: "Card validated successfully"
            }
        };
    }



    async composeWebhookData(transId: string, statusCode: number, message: string) {

        const transaction: any = await this.selectDataQuery("transactions", `trans_id = '${transId}'`);
        if (transaction.length === 0) {
            console.error(`Transaction not found for ID: ${transId}`);
            return false;
        }
        const sc_transactions = await this.selectDataQuery("sc_transactions", `refId = '${transId}'`);

        let webhookData: WebhookData = {
            type: "transaction_status",
            statusCode: statusCode,
            message: message,
            timestamp: this.formatedDate(new Date()),
            trans_type: transaction[0].trans_type,
            reference_id: transaction[0].reference_id,
            status: transaction[0].status,
            amount: transaction[0].amount,
            client_id: transaction[0].client_id,
            currency: transaction[0].currency,
            sender_account: transaction[0].sender_account,
            receiver_account: transaction[0].receiver_account,
            transaction_id: transId,
            fee: transaction[0].fee,
            meta: transaction[0].memo,

        };

        if (sc_transactions.length > 0) {
            webhookData.chainInfo = {
                from_address: sc_transactions[0].source,
                to_address: sc_transactions[0].destination,
                amount: sc_transactions[0].amount,
                asset_code: sc_transactions[0].asset,
                contract_address: sc_transactions[0].network || "",
                hash: sc_transactions[0].hash,
                state: sc_transactions[0].state,
                direction: sc_transactions[0].direction
            }
        }

        return webhookData;
    }
    formatedDate(updated_at: any) {
        return new Date(updated_at).toISOString().slice(0, 19).replace('T', ' ')
    }

    async saveWebhookLog(cl_id: any, clientId: any, transId: any, callbackUrl: any, event: any, webhookData: any, status: any, direction: any = "OUTGOING", provider = "MUDA") {
        try {
            await this.insertData("webhook_logs", {
                cl_id: cl_id,
                client_id: clientId,
                trans_id: transId,
                webhook_url: callbackUrl,
                event: event,
                webhook_data: JSON.stringify(webhookData),
                status_code: status,
                timestamp: this.formatedDate(new Date()),
                direction: direction,
                provider: provider
            });
        } catch (error: any) {
            console.error(`❌ Error in saveWebhookLog:`, error);
            return false;
        }
    }

    async updateWebhookLog(cl_id: any, response: any, response_code: any, updated_at: any) {
        try {
            const data = {
                response: JSON.stringify(response),
                response_code: response_code,
                updated_at: this.formatedDate(new Date())
            }
            await this.updateData("webhook_logs", `cl_id = '${cl_id}'`, data)
        } catch (error: any) {
            console.error(`❌ Error in updateWebhookLog:`, error);
            return false;
        }
    }

    async sendWebhook(transId: any, clientId: any, callbackUrl: any, event: any, status: any, webhookData: WebhookData) {

        const cl_id = this.getTransId()

        try {
            await this.saveWebhookLog(cl_id, clientId, transId, callbackUrl, event, webhookData, 202, "MUDA")

            const response = await post(callbackUrl, webhookData);
            await this.updateWebhookLog(cl_id, response, response.status, this.formatedDate(new Date()))
            return response;
        } catch (error: any) {
            await this.updateWebhookLog(cl_id, error, error.status || 500, this.formatedDate(new Date()))
            return false;
        }
    }

    async saveWebhook(
        status: number,
        clientId: string,
        transId: string,
        type: string,
        status_value: string,
        message: string
    ) {
        try {
            const clientInfo: any = await this.selectDataQuery(`webhooks`, `client_id='${clientId}'`);
            if (clientInfo.length === 0) {
                console.error(`No webhook URL found for client ID: ${clientId}`);
                return false;
            }


            const webhookData: any = await this.composeWebhookData(transId, status, message);
            if (!webhookData || webhookData == false) {
                console.error(`Failed to compose webhook data for transaction ID: ${transId}`);
                return false;
            }
            for (let i = 0; i < clientInfo.length; i++) {
                const callbackUrl = clientInfo[i]['callback_url'];
                await this.sendWebhook(transId, clientId, callbackUrl, type, status, webhookData);
            }
            return true

        } catch (error: any) {
            console.error(`❌ Error in saveWebhook:`, error.message);
            return false;
        }
    }

    /**
     * Log third-party request
     * @param data Request data
     */
    async logThirdPartyResponse(
        trans_id: string,
        service_name: 'MOBILE_MONEY' | 'BANK_TRANSFER' | 'INTERAC' | 'CARD_PAYMENT' | 'CRYPTO_PAYOUT' | 'CRYPTO_COLLECTION',
        log_type: 'REQUEST' | 'RESPONSE' | 'ERROR',
        request_type: 'PUSH' | 'PULL' | 'VALIDATION' | 'STATUS',
        error_message: string = "",
        data: any = {}
    ) {
        try {
            const logData = {
                trans_id: trans_id,
                request_data: data,
                log_type: log_type,
                service_name: service_name,
                error_message: error_message,
                request_type: request_type
            }
            await this.insertData("third_party_logs", logData);
            return true;
        } catch (error) {
            console.error("Error logging third-party request:", error);
            return false;
        }
    }





    // ==================== TEMBO SERVICE METHODS ====================
    // Following existing integration patterns from processMobileMoneyPayout, processInteracPayout

    /**
     * Process Tembo mobile money payout
     * Simplified to just use the renamed makeMMPushRequest method
     */
    private async processTemboMobilePayout(data: {
        trans_id: string;
        clientId: string;
        amount: string;
        phone: string;
        currency: string;
        extra_data?: any;
    }): Promise<ThirdPartyResponseBody> {
        try {
            logger.info(`🔹 Processing Tembo mobile payout`, { trans_id: data.trans_id, phone: data.phone });

            // Call TemboService.makeMMPushRequest - just the renamed method
            const temboResponse = await this.temboService.makeMMPushRequest(
                data.clientId, // Use actual clientId as userId
                data.trans_id,
                data.amount,
                data.phone
            );

            // Log the response - following existing pattern
            await this.logThirdPartyResponse(
                data.trans_id,
                'MOBILE_MONEY',
                'RESPONSE',
                'PUSH',
                '',
                temboResponse
            );

            return temboResponse;
        } catch (error: any) {
            logger.error(`❌ Tembo mobile payout failed`, { trans_id: data.trans_id, error: error.message });

            await this.logThirdPartyResponse(
                data.trans_id,
                'MOBILE_MONEY',
                'ERROR',
                'PUSH',
                error.message,
                error
            );

            return {
                status: 500,
                message: error.message || 'Tembo mobile payout failed',
                data: error
            };
        }
    }


    /**
     * Process Tembo collection (for collections workflow)
     * Simplified to just use the renamed makeMMPullRequest method
     */
    async processTemboCollection(data: {
        trans_id: string;
        clientId: string;
        amount: string;
        phone: string;
        currency: string;
        service_name: string;
    }): Promise<ThirdPartyResponseBody> {
        try {
            logger.info(`🔹 Processing Tembo collection`, { trans_id: data.trans_id, phone: data.phone });

            // Call TemboService.makeMMPullRequest - just the renamed method
            const temboResponse = await this.temboService.makeMMPullRequest(
                data.clientId, // Use actual clientId as userId
                data.trans_id,
                data.amount,
                data.phone
            );

            // Log the response - following existing pattern
            await this.logThirdPartyResponse(
                data.trans_id,
                'MOBILE_MONEY',
                'RESPONSE',
                'PULL',
                '',
                temboResponse
            );

            return temboResponse;
        } catch (error: any) {
            logger.error(`❌ Tembo collection failed`, { trans_id: data.trans_id, error: error.message });

            await this.logThirdPartyResponse(
                data.trans_id,
                'MOBILE_MONEY',
                'ERROR',
                'PULL',
                error.message,
                error
            );

            return {
                status: 500,
                message: error.message || 'Tembo collection failed',
                data: error
            };
        }
    }

    /**
     * Get Tembo account balance
     * Follows pattern from existing balance methods
     */
    async getTemboBalance(accountNo: string): Promise<ThirdPartyResponseBody> {
        try {
            logger.info(`🔹 Getting Tembo balance`, { accountNo });

            const temboResponse = await this.temboService.getVirtualAccountBalance(accountNo);

            return temboResponse;
        } catch (error: any) {
            logger.error(`❌ Tembo balance check failed`, { accountNo, error: error.message });

            return {
                status: 500,
                message: error.message || 'Tembo balance check failed',
                data: error
            };
        }
    }

    /**
     * Process Tembo webhook
     * Follows pattern from existing webhook handlers
     *
     * CRITICAL: Tembo webhooks indicate funds received in main collection account.
     * Must find the merchant transaction by reference and attribute funds correctly.
     */
    async processTemboWebhook(
        payload: any,
        signature: string,
        timestamp: string
    ): Promise<ThirdPartyResponseBody> {
        try {
            logger.info(`🔹 Processing Tembo webhook`, {
                reference: payload.reference,
                amount: payload.amountCredit,
                note: 'Funds received in Tembo main account - need merchant attribution'
            });

            // First, process the webhook through TemboService for validation
            const temboResponse = await this.temboService.processWebhook(
                payload,
                signature,
                timestamp
            );

            if (temboResponse.status !== 200) {
                return temboResponse;
            }

            // CRITICAL: Find the merchant transaction using the reference
            // This follows the same pattern as other webhook processors
            const transactionRef = payload.reference;
            const merchantTransaction = await this.findMerchantTransactionByReference(transactionRef);

            if (!merchantTransaction) {
                logger.error(`❌ No merchant transaction found for Tembo reference`, {
                    reference: transactionRef
                });
                return {
                    status: 404,
                    message: 'Merchant transaction not found for this reference',
                    data: { reference: transactionRef, payload }
                };
            }

            // Extract merchant information
            const { client_id: merchantId, amount: expectedAmount, currency } = merchantTransaction;

            // Get the actual received amount from the processed webhook data
            const receivedAmount = temboResponse.amount_transfered || 0;
            const transactionDirection = temboResponse.data?.transaction_direction || 'UNKNOWN';

            logger.info(`💰 Attributing Tembo collection to merchant`, {
                merchantId,
                expectedAmount,
                receivedAmount,
                reference: transactionRef,
                note: 'Funds received in collection account - will transfer to merchant virtual account'
            });

            // Validate amount matches (with small tolerance for fees)
            const amountDifference = Math.abs(parseFloat(expectedAmount) - receivedAmount);
            if (amountDifference > 1) { // Allow 1 TZS tolerance
                logger.warn(`⚠️ Amount mismatch in Tembo collection`, {
                    expected: expectedAmount,
                    received: receivedAmount,
                    difference: amountDifference
                });
            }

            // CRITICAL: Transfer funds from collection account to merchant virtual account
            await this.transferFundsToMerchant(merchantTransaction, payload);

            // Update the transaction status and trigger merchant credit
            await this.processMerchantCollectionCredit(merchantTransaction, payload);

            return {
                status: 200,
                message: 'Tembo collection attributed to merchant successfully',
                trans_id: transactionRef,
                amount_transfered: receivedAmount,
                data: {
                    merchantId,
                    originalTransaction: merchantTransaction,
                    temboWebhook: payload,
                    attribution: 'SUCCESS'
                }
            };

        } catch (error: any) {
            logger.error(`❌ Tembo webhook processing failed`, { error: error.message });

            return {
                status: 500,
                message: error.message || 'Tembo webhook processing failed',
                data: error
            };
        }
    }

    /**
     * Find merchant transaction by reference
     * Follows pattern from existing webhook processors
     */
    private async findMerchantTransactionByReference(reference: string): Promise<any> {
        try {
            const transactions = await this.selectDataQuery(
                'transactions',
                `trans_id='${reference}' AND status IN ('PENDING', 'INITIATED')`
            );

            return transactions.length > 0 ? transactions[0] : null;
        } catch (error: any) {
            logger.error('Failed to find merchant transaction', { reference, error: error.message });
            return null;
        }
    }

    /**
     * Transfer funds from collection account to merchant virtual account
     * CRITICAL: This is the key step that moves funds from Muda's collection account
     * to the individual merchant's virtual account
     */
    private async transferFundsToMerchant(transaction: any, temboPayload: any): Promise<void> {
        try {
            const { trans_id, client_id, amount } = transaction;
            const receivedAmount = temboPayload.amountCredit;

            // Get merchant's virtual account number
            // In production, this should be retrieved from database
            const merchantAccountNo = await this.getMerchantVirtualAccount(client_id);

            if (!merchantAccountNo) {
                logger.error(`❌ No virtual account found for merchant`, { client_id });
                throw new Error(`No virtual account configured for merchant: ${client_id}`);
            }

            logger.info(`🔄 Transferring funds to merchant virtual account`, {
                transId: trans_id,
                merchantId: client_id,
                merchantAccount: merchantAccountNo,
                amount: receivedAmount,
                note: 'Moving funds from collection account to merchant account'
            });

            // Transfer funds using TemboService
            const transferResult = await this.temboService.transferToMerchantAccount(
                trans_id,
                merchantAccountNo,
                receivedAmount,
                `Collection transfer for ${trans_id}`
            );

            if (transferResult.status !== 200) {
                throw new Error(`Fund transfer failed: ${transferResult.message}`);
            }

            logger.info(`✅ Funds transferred to merchant account`, {
                transId: trans_id,
                merchantId: client_id,
                merchantAccount: merchantAccountNo,
                amount: receivedAmount
            });

        } catch (error: any) {
            logger.error('Failed to transfer funds to merchant', {
                transaction,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Get merchant's virtual account number from database
     * TODO: Implement proper database lookup
     */
    private async getMerchantVirtualAccount(merchantId: string): Promise<string | null> {
        try {
            // In production, this would query the database:
            // const merchant = await this.selectDataQuery('merchants', `client_id='${merchantId}'`);
            // return merchant[0]?.tembo_virtual_account;

            // For now, use environment variable pattern for testing
            const accountNo = process.env[`TEMBO_MERCHANT_ACCOUNT_${merchantId}`];
            return accountNo || null;

        } catch (error: any) {
            logger.error('Failed to get merchant virtual account', { merchantId, error: error.message });
            return null;
        }
    }

    /**
     * Process merchant collection credit
     * Updates transaction status and triggers merchant account credit
     */
    private async processMerchantCollectionCredit(transaction: any, temboPayload: any): Promise<void> {
        try {
            const { trans_id, client_id, amount, currency } = transaction;

            // Update transaction status to SUCCESS
            await this.updateData('transactions', `trans_id='${trans_id}'`, {
                status: 'SUCCESS',
                ext_reference: temboPayload.transactionId,
                completed_at: new Date().toISOString()
            });

            // Log the successful collection
            await this.logThirdPartyResponse(
                trans_id,
                'MOBILE_MONEY',  // Use existing service type
                'RESPONSE',
                'PULL',
                'Collection successful - funds in Tembo main account',
                temboPayload
            );

            // Send webhook to merchant
            await this.saveWebhook(
                200,
                client_id,
                trans_id,
                'COLLECTION',
                'SUCCESS',
                'Collection completed successfully'
            );

            logger.info(`✅ Merchant collection credited`, {
                transId: trans_id,
                merchantId: client_id,
                amount,
                currency,
                note: 'Funds attributed from Tembo main account'
            });

        } catch (error: any) {
            logger.error('Failed to process merchant collection credit', {
                transaction,
                error: error.message
            });
            throw error;
        }
    }

}

export default ThirdPartyHandler;