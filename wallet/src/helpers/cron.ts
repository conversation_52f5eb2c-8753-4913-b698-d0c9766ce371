import * as cron from 'node-cron';
import Transactions from '../models/transactions';
import PegaPay from '../intergrations/PegPay';
import ThirdPartyHandler from './ThirdPartyHandler';
import MyFX from '../intergrations/MyFX';
import Model, { Steps } from './model';
import { StatusCodes } from '../intergrations/interfaces';
import { get } from './httpRequest';

class CronService {
    constructor() {
        console.log("Cron Service initiated.");
        this.scheduleEveryThirtySeconds();
        this.scheduleDailyCacheNews();
        this.scheduleDailySummaryTask();
        this.scheduleOfflineTransactionProcessing();
        this.scheduleEverySixHours();
        this.scheduleEveryMinute();
    }

    private scheduleEverySixHours() {
        cron.schedule('0 */6 * * *', async () => {
            console.log('Running every six hours task...');
            try {
                MyFX.getJWT()
                // Add your logic here
                console.log('Every six hours task completed.');
            } catch (error) {
                console.error('Error running every six hours task:', error);
            }
        });
    }

    private scheduleEveryMinute() {
        cron.schedule('* * * * *', async () => {
            console.log('Running every minute task...');
            try {
                const transactions = new Transactions();
                transactions.ReverseTransaction({
                    token: "mdxQaUg",
                    userId: "123"
                })

                // Add your logic here
                console.log('Every minute task completed.');
            } catch (error) {
                console.error('Error running every minute task:', error);
            }
        });
    }

    private scheduleEveryThirtySeconds() {
        cron.schedule('*/30 * * * * *', this.everyThirtySecondsTask);
    }

    private scheduleDailyCacheNews() {
        cron.schedule('0 0 * * *', async () => {
            console.log('Running daily cacheNews task...');
            try {
                console.log('Daily cacheNews task completed.');
            } catch (error) {
                console.error('Error running daily cacheNews task:', error);
            }
        });
    }

    private scheduleDailySummaryTask() {
        cron.schedule('0 6 * * *', async () => { // Runs daily at 1:00 AM
            console.log('Running daily summary task...');
            try {
                console.log('Daily summary task completed.');
            } catch (error) {
                console.error('Error running daily summary task:', error);
            }
        });
    }

    private scheduleOfflineTransactionProcessing() {
        // Process offline transactions every 5 minutes
        cron.schedule('*/5 * * * *', async () => {
            console.log('Processing offline transactions...');
            try {
                // await this.processOfflineTransactions();
                console.log('Offline transaction processing completed.');
            } catch (error) {
                console.error('Error processing offline transactions:', error);
            }
        });
    }

    private everyThirtySecondsTask = () => {
        const transactions = new Transactions();
        this.checkPendingDirectPayouts();
        transactions.getPendingTransactions();
        this.checkPendingLiquidityRailPayouts();
        console.log('Task running every 30 seconds, checking pending transactions...');
    };


    // Status Code Arrays

    // Function to check transaction status
    checkTransactionStatus(statusCode: any) {
        const SUCCESS_CODES = ["0"];
        const FAILED_CODES = [
            "1", "2", "3", "4", "5", "6", "7", "8", "10", "11", "12", "13", "14", "15", "20", "22",
            "25", "26", "27", "28", "30", "31", "32", "33", "34", "35", "36", "37", "2000", "202", "100"
        ];
        const PENDING_CODES = ["122", "9", "17", "21"];
        const ONHOLD_CODES = ["11"];
        //const RETRY_CODES = ["16"]
        const RETRY_CODES = ["567890"]

        if (SUCCESS_CODES.includes(statusCode)) {
            return "SUCCESS"
        } else if (FAILED_CODES.includes(statusCode)) {
            return "FAILED"
        } else if (PENDING_CODES.includes(statusCode)) {
            return "PENDING"
        } else if (ONHOLD_CODES.includes(statusCode)) {
            return "ONHOLD"
        } else if (RETRY_CODES.includes(statusCode)) {
            return "RETRY"
        } else {
            return "ONHOLD"
        }
    }


    private async checkPendingLiquidityRailPayouts() {
        console.log(`checkPendingLiquidityRailPayouts`)
        try {
            const transactions = new Transactions();
            const pendingTransactions = await transactions.selectDataQuery(
                "transactions",
                `status = 'PROCESSING' AND trans_type='PUSH' and service_name='LIQUIDITY_RAIL'`
            );

            console.log(`Found ${pendingTransactions.length} pending direct payouts to check`);

            for (const transaction of pendingTransactions) {
                const trans_id = transaction.trans_id;

                const resp: any = await this.checkLiquidityRailPayout(trans_id)
                if (resp.status == 200) {
                    await transactions.updateTransaction(trans_id, "SUCCESS", StatusCodes.SUCCESS.message)
                } else if (resp.status == 400) {
                    await transactions.updateTransaction(trans_id, "FAILED", resp.message)
                } else if (resp.status == 202) {
                    //  await transactions.updateTransaction(trans_id, "PENDING", resp.message)
                }

            }
        } catch (error) {
            console.error('Error checking pending Liquidity Rail payouts:', error);
        }
    }
    async checkLiquidityRailPayout(trans_id: any) {
        const transinfo = await get(process.env.LIQUIDITY_RAIL_API_URL + `/accounts/transactions/${trans_id}`)
        console.log(`checkLiquidityRailPayout`, trans_id)
        const transaction = transinfo.data
        const status = transaction.status
        

        if (status == "SUCCESSFUL") {
            return {
                status: 200,
                message: "Transaction completed successfully"
            }
        } else if (status == "FAILED" || status == "EXPIRED") {
            return {
                status: 400,
                message: "Transaction failed"
            }
        } else if (status == "PENDING") {
            return {
                status: 202,
                message: "Transaction pending"
            }
        }


    }

    private async checkPendingDirectPayouts() {
        console.log(`checkPendingDirectPayouts`)
        try {
            const transactions = new Transactions();
            const pendingTransactions = await transactions.selectDataQuery(
                "transactions",
                `status = 'PENDING' AND trans_type='PUSH' and service_name='MOBILE_MONEY'`
            );

            console.log(`Found ${pendingTransactions.length} pending direct payouts to check`);

            for (const transaction of pendingTransactions) {
                const trans_id = transaction.trans_id;
                const client_id = transaction.client_id;
                const amount = transaction.amount;
                const receiver_account = transaction.receiver_account;
                const service_name = transaction.service_name;
                const SessionId = transaction.SessionId;
                const currency = transaction.currency;


                // Get transaction status from payment provider
                const pgStatus: any = await new PegaPay().getTransactionDetails(trans_id, "PUSH");
                console.log(`pullRequest`, pgStatus)
                const pendingStatus = ["122", "9", "16", "11", "122"]
                const failedCodes = ["122", "9", "16", "11", "122"]

                // 11 contact pegaus. HOLD transaction


                const description = await new Model().mapDescription(pgStatus.description)
                if (pgStatus && typeof pgStatus === 'object' && 'statusCode' in pgStatus) {
                    console.log(`pgStatus::::`, trans_id, pgStatus.statusCode, pgStatus.description)

                    const transStatus = this.checkTransactionStatus(pgStatus.statusCode);


                    if (pgStatus.statusCode === "0" && pgStatus.description === "SUCCESS") {
                        await new Model().saveTransactionLog(trans_id, "SUCCESS", Steps.TRANS_STATUS_CHECK, 200, "Transaction confirmed", pgStatus)
                        await transactions.updateTransaction(trans_id, "SUCCESS", description)
                        await new ThirdPartyHandler().saveWebhook(200, client_id, trans_id, "transaction_status", "SUCCESS", "Transaction completed successfully")
                        console.log(`Transaction ${trans_id} completed successfully`);
                    } else if (transStatus === "FAILED") {
                        await new Model().saveTransactionLog(trans_id, "FAILED", Steps.TRANS_STATUS_CHECK, 400, "Transaction failed", pgStatus)
                        await new ThirdPartyHandler().saveWebhook(400, client_id, trans_id, "transaction_status", "FAILED", description)
                        // Transaction failed
                        await new Model().updateTransaction(trans_id, "FAILED", transStatus, description)

                    } else if (transStatus === "PENDING") {



                    } else if (transStatus === "ONHOLD" || transStatus === "RETRY") {
                        await new Model().saveTransactionLog(trans_id, "ONHOLD", Steps.TRANS_STATUS_CHECK, 203, "Transaction on hold", pgStatus)
                        await new ThirdPartyHandler().saveWebhook(203, client_id, trans_id, "transaction_status", "ONHOLD", description)
                        await new Model().updateTransaction(trans_id, "ONHOLD", description)
                    }
                }
            }

        } catch (error) {
            console.error('Error checking pending direct payouts:', error);
        }
    }


}

export default CronService;