
export interface CallbackData {
    type: "transaction_status" | "payment_confirmation" | "wallet_update"; // Callback Type
    timestamp: string; // ISO Timestamp
    reference_id: string; // Unique reference ID for tracking
    status: "PENDING" | "SUCCESS" | "FAILED"; // Transaction or payment status
    amount?: string; // Optional: Transaction amount
    currency?: string; // Optional: Currency or token code (e.g., "UGX")
    sender_account?: string; // Optional: Public Key or Account ID of sender
    receiver_account?: string; // Optional: Public Key or Account ID of receiver
    transaction_id?: string; // Optional: Stellar or Internal Transaction ID
     meta?: string; // Optional: Any additional notes
     client_id:string
}
export interface TransactionInterface {
    reference_id: string,
    validation_id: string,
    product_id: string,
    client_id: string,
    trans_type: string 
    trans_id: string,
    amount: string,
    asset_code: string,
    fee: string,
    currency: string,
    provider_fees: string,
    req_amount: string,
    service_name: string,
    sender_account: string,
    receiver_account: string,
    memo: string,
    payment_method_id: string,
    running_balance: string,
    status: string,
    SessionId: string,
    receive_currency?: string
}
export interface TransactionInterfaceMini {
    trans_id: string,
    reference_id: string,
    validation_id: string,
    product_id: string,
    client_id: string,
    trans_type: string,
    amount: string,
    receiver_account: string,
    service_name: string,
    currency: string,
    memo?: string
}

export const systemProductCodes = {
    BANK_DEPOSIT: "50001",
    BANK_WITHDRAWAL: "50002",
    SWAP_FROM: "50003",
    SWAP_TO: "50004"
}