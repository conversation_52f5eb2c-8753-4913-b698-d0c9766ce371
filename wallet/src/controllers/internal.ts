import express, { Request, Response } from "express";
import { JWTMiddleware } from "../helpers/jwt.middleware";
import Internal from "../models/internal";


const router = express.Router();
const internal = new Internal();


const applyJWTConditionally = (req: Request, res: Response, next: any) => {
    JWTMiddleware.verifyTokenAccess(req, res, next);
    // next()
};

const saveApiLog = (req: Request, res: Response, next: any) => {
    // thirdPartyHandler.saveApiLog(req.body, req.ip || "");  
    next();
}
router.post("/send-callback",  sendCallBack);




async function sendCallBack(req: Request, res: Response) {
  try {
    const result = await internal.sendCallBack(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}




export default router;
