# Tembo Integration Test Environment Configuration
# 
# This file contains the environment variables needed for running
# comprehensive integration tests with the Tembo API.
# 
# IMPORTANT: These should be SANDBOX/TEST credentials only!
# Never use production credentials in test files.

# =============================================================================
# TEMBO API CONFIGURATION (SANDBOX)
# =============================================================================

# Tembo API Base URL (Sandbox)
TEMBO_API_URL=https://sandbox.temboplus.com

# Tembo Authentication Token (Get from Tembo sandbox dashboard)
TEMBO_AUTH_TOKEN=your_sandbox_auth_token_here

# Tembo Account ID (Your sandbox account identifier)
TEMBO_ACCOUNT_ID=your_sandbox_account_id_here

# Tembo Secret Key (For API authentication)
TEMBO_SECRET_KEY=your_sandbox_secret_key_here

# Tembo Webhook Secret (For webhook signature verification)
TEMBO_WEBHOOK_SECRET=your_sandbox_webhook_secret_here

# Default Tembo Account for Payouts (Your sandbox disbursement account)
TEMBO_DEFAULT_ACCOUNT=your_sandbox_disbursement_account_here

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================

# Base URL for webhook callbacks (will be set dynamically in tests)
WEBHOOK_BASE_URL=http://localhost:3000

# =============================================================================
# TEST-SPECIFIC CONFIGURATION
# =============================================================================

# Test phone numbers (Use Tembo sandbox test numbers)
TEST_PHONE_VODACOM=************
TEST_PHONE_TIGO=************
TEST_PHONE_AIRTEL=************

# Test amounts (Small amounts for testing)
TEST_AMOUNT_SMALL=1000
TEST_AMOUNT_MEDIUM=5000
TEST_AMOUNT_LARGE=10000

# Test merchant identifiers
TEST_MERCHANT_ID=test_merchant_001
TEST_MERCHANT_NAME=Test Merchant Ltd

# =============================================================================
# DATABASE CONFIGURATION (if needed for tests)
# =============================================================================

# Test database connection (if your tests need database access)
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=wallet_test
# DB_USER=test_user
# DB_PASSWORD=test_password

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level for tests
LOG_LEVEL=debug

# Enable detailed API logging for debugging
ENABLE_API_LOGGING=true

# =============================================================================
# TIMEOUT CONFIGURATION
# =============================================================================

# API request timeout (milliseconds)
API_TIMEOUT=30000

# Webhook wait timeout (milliseconds)
WEBHOOK_TIMEOUT=45000

# =============================================================================
# FEATURE FLAGS FOR TESTING
# =============================================================================

# Enable/disable specific test features
ENABLE_WEBHOOK_TESTS=true
ENABLE_PAYOUT_TESTS=true
ENABLE_COLLECTION_TESTS=true
ENABLE_BALANCE_TESTS=true

# =============================================================================
# INSTRUCTIONS FOR SETUP
# =============================================================================

# 1. Get Tembo Sandbox Credentials:
#    - Sign up for Tembo sandbox account
#    - Get your API credentials from the dashboard
#    - Replace the placeholder values above

# 2. Configure Webhook URL:
#    - The tests will start a local webhook server
#    - Update your Tembo sandbox webhook URL to point to your test server
#    - Format: http://your-ngrok-url.ngrok.io/webhook/tembo

# 3. Test Phone Numbers:
#    - Use Tembo's provided test phone numbers
#    - These numbers simulate real mobile money transactions
#    - Check Tembo documentation for valid test numbers

# 4. Run Tests:
#    npm test -- tembo.integration.test.ts

# =============================================================================
# SECURITY NOTES
# =============================================================================

# - This file contains SANDBOX credentials only
# - Never commit production credentials to version control
# - Use environment-specific configuration in CI/CD
# - Rotate test credentials regularly
# - Monitor test API usage to avoid rate limits
