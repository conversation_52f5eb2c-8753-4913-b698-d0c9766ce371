import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import express from 'express';
import TemboService from '../src/intergrations/Tembo';
import ThirdPartyHandler from '../src/helpers/ThirdPartyHandler';
import { Server } from 'http';

/**
 * Tembo Integration Tests
 *
 * This test suite performs REAL integration testing with Tembo API:
 * - Tests TemboService methods directly
 * - Tests ThirdPartyHandler integration
 * - Tests webhook processing end-to-end
 * - Uses actual Tembo sandbox environment
 * - No mocking - real API calls
 *
 * Prerequisites:
 * - .env.test file with valid Tembo sandbox credentials
 * - Tembo sandbox account configured
 * - Valid test phone numbers for Tanzania
 */

// Track test start time for monitoring
const testStartTime = Date.now();

describe('Tembo Integration Tests', () => {
  let temboService: TemboService;
  let thirdPartyHandler: ThirdPartyHandler;
  let webhookApp: express.Application;
  let webhookServer: Server;
  let webhookPort: number;
  let receivedWebhooks: any[] = [];

  // Test configuration
  const testConfig = {
    testPhone: '************', // Valid Tanzanian test number
    testAmount: '1000', // Small amount for testing
    testMerchantId: 'test_merchant_001',
    webhookTimeout: 30000, // 30 seconds to wait for webhooks
  };

  beforeAll(async () => {
    // Load test environment
    require('dotenv').config({ path: '.env.test' });
    
    // Verify test environment is configured
    expect(process.env.TEMBO_AUTH_TOKEN).toBeDefined();
    expect(process.env.TEMBO_ACCOUNT_ID).toBeDefined();
    expect(process.env.TEMBO_SECRET_KEY).toBeDefined();
    expect(process.env.WEBHOOK_BASE_URL).toBeDefined();
    
    // Initialize services
    temboService = new TemboService();
    thirdPartyHandler = new ThirdPartyHandler();
    
    // Setup webhook server for testing
    await setupWebhookServer();
    
    console.log('🚀 Tembo integration tests initialized');
    console.log(`📡 Webhook server running on port ${webhookPort}`);
  });

  afterAll(async () => {
    // Cleanup webhook server
    if (webhookServer) {
      webhookServer.close();
    }
    console.log('🧹 Test cleanup completed');
  });

  /**
   * Setup webhook server to capture Tembo webhooks during testing
   */
  async function setupWebhookServer(): Promise<void> {
    webhookApp = express();
    webhookApp.use(express.json());
    
    // Webhook endpoint that captures all incoming webhooks
    webhookApp.post('/webhook/tembo', (req, res) => {
      console.log('📥 Webhook received:', {
        headers: req.headers,
        body: req.body,
        timestamp: new Date().toISOString()
      });
      
      receivedWebhooks.push({
        headers: req.headers,
        body: req.body,
        timestamp: new Date().toISOString()
      });
      
      // Always respond with 200 to acknowledge receipt
      res.status(200).json({ status: 'received' });
    });
    
    // Start server on random available port
    return new Promise((resolve) => {
      webhookServer = webhookApp.listen(0, () => {
        const address = webhookServer.address();
        webhookPort = typeof address === 'object' && address ? address.port : 3000;
        resolve();
      });
    });
  }

  /**
   * Wait for webhook to be received
   */
  async function waitForWebhook(transactionRef: string, timeoutMs: number = testConfig.webhookTimeout): Promise<any> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeoutMs) {
      const webhook = receivedWebhooks.find(w => 
        w.body.reference === transactionRef
      );
      
      if (webhook) {
        return webhook;
      }
      
      // Wait 1 second before checking again
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    throw new Error(`Webhook not received within ${timeoutMs}ms for transaction ${transactionRef}`);
  }

  describe('TemboService Direct Tests', () => {
    test('should initialize with valid environment variables', () => {
      expect(temboService).toBeInstanceOf(TemboService);
      // Test that service initializes without throwing errors
      expect(temboService).toBeDefined();
    });

    test('should get collection balance', async () => {
      const result = await temboService.getCollectionBalance();
      
      expect(result).toBeDefined();
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('availableBalance');
      expect(result.data).toHaveProperty('currentBalance');
      expect(result.data).toHaveProperty('accountNo');
      
      console.log('💰 Collection Balance:', result.data);
    }, 10000);

    test('should create virtual account for merchant', async () => {
      const result = await temboService.createVirtualAccount(
        testConfig.testMerchantId,
        {
          companyName: 'Test Merchant Ltd',
          tag: 'TEST'
        }
      );
      
      expect(result).toBeDefined();
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('accountNo');
      
      console.log('🏦 Virtual Account Created:', result.data);
    }, 15000);
  });

  describe('Collection Flow Tests', () => {
    test('should process collection via ThirdPartyHandler', async () => {
      const transactionId = `TEST_COLLECTION_${Date.now()}`;
      
      // Clear previous webhooks
      receivedWebhooks = [];
      
      // Process collection through ThirdPartyHandler
      const collectionData = {
        trans_id: transactionId,
        clientId: testConfig.testMerchantId,
        amount: testConfig.testAmount,
        phone: testConfig.testPhone,
        currency: 'TZS',
        service_name: 'TEMBO_COLLECTION'
      };
      
      const result = await thirdPartyHandler.processTemboCollection(collectionData);
      
      expect(result).toBeDefined();
      expect(result.status).toBe(200);
      expect(result.trans_id).toBe(transactionId);
      
      console.log('📱 Collection initiated:', result);
      
      // Wait for webhook (this tests the full flow)
      console.log('⏳ Waiting for collection webhook...');
      const webhook = await waitForWebhook(transactionId);
      
      expect(webhook).toBeDefined();
      expect(webhook.body.reference).toBe(transactionId);
      expect(webhook.body.amountCredit).toBeGreaterThan(0);
      
      console.log('✅ Collection webhook received:', webhook.body);
    }, 45000);

    test('should process webhook and attribute to merchant', async () => {
      const transactionId = `TEST_WEBHOOK_${Date.now()}`;
      
      // Simulate a Tembo webhook payload
      const webhookPayload = {
        accountNo: '*************',
        payerName: 'TEST CUSTOMER',
        id: `${Date.now()}-test-uuid`,
        transactionId: `tembo_${Date.now()}`,
        reference: transactionId,
        transactionType: 'H4',
        channel: 'CMM',
        transactionDate: new Date().toISOString(),
        postingDate: new Date().toISOString(),
        valueDate: new Date().toISOString(),
        narration: `Test collection for ${transactionId}`,
        currency: 'TZS',
        amountCredit: 1000,
        amountDebit: 0,
        clearedBalance: 1000000,
        bookedBalance: 1000000
      };
      
      // Create a mock transaction in the system first
      // (In real scenario, this would be created when collection is initiated)
      
      // Process webhook through ThirdPartyHandler
      const result = await thirdPartyHandler.processTemboWebhook(
        webhookPayload,
        'test_signature',
        Date.now().toString()
      );
      
      expect(result).toBeDefined();
      expect(result.status).toBe(200);
      expect(result.trans_id).toBe(transactionId);
      
      console.log('🎯 Webhook processed:', result);
    }, 15000);
  });

  describe('Payout Flow Tests', () => {
    test('should process payout via ThirdPartyHandler', async () => {
      const transactionId = `TEST_PAYOUT_${Date.now()}`;
      
      // Clear previous webhooks
      receivedWebhooks = [];
      
      // Process payout through ThirdPartyHandler using public handlePayout method
      const payoutData = {
        trans_id: transactionId,
        clientId: testConfig.testMerchantId,
        amount: testConfig.testAmount,
        phone: testConfig.testPhone,
        SessionId: '0000',
        service_name: 'TEMBO_MOBILE',
        currency: 'TZS',
        extra_data: {
          recipient_name: 'Test Customer'
        }
      };

      const result = await thirdPartyHandler.handlePayout(payoutData);
      
      expect(result).toBeDefined();
      expect(result.status).toBe(200);
      expect(result.trans_id).toBe(transactionId);
      
      console.log('💸 Payout initiated:', result);
      
      // Wait for webhook (this tests the full flow)
      console.log('⏳ Waiting for payout webhook...');
      const webhook = await waitForWebhook(transactionId);
      
      expect(webhook).toBeDefined();
      expect(webhook.body.reference).toBe(transactionId);
      
      console.log('✅ Payout webhook received:', webhook.body);
    }, 45000);
  });

  describe('Error Handling Tests', () => {
    test('should handle invalid phone number gracefully', async () => {
      const transactionId = `TEST_ERROR_${Date.now()}`;
      
      const collectionData = {
        trans_id: transactionId,
        clientId: testConfig.testMerchantId,
        amount: testConfig.testAmount,
        phone: '123456789', // Invalid phone number
        currency: 'TZS',
        service_name: 'TEMBO_COLLECTION'
      };
      
      const result = await thirdPartyHandler.processTemboCollection(collectionData);
      
      // Should handle error gracefully
      expect(result).toBeDefined();
      expect(result.status).toBeGreaterThanOrEqual(400);
      
      console.log('❌ Error handled:', result);
    }, 15000);

    test('should handle webhook signature verification', async () => {
      const webhookPayload = {
        reference: 'TEST_INVALID_SIG',
        amountCredit: 1000,
        // ... other fields
      };
      
      // Test with invalid signature
      const result = await thirdPartyHandler.processTemboWebhook(
        webhookPayload,
        'invalid_signature',
        Date.now().toString()
      );
      
      expect(result).toBeDefined();
      // Should handle invalid signature appropriately
      
      console.log('🔒 Signature validation result:', result);
    }, 10000);
  });

  describe('Network Detection Tests', () => {
    test('should detect mobile networks correctly', () => {
      // Test network detection logic
      const testCases = [
        { phone: '************', expectedNetwork: 'VODACOM' },
        { phone: '255687123456', expectedNetwork: 'TIGO' },
        { phone: '255744123456', expectedNetwork: 'AIRTEL' }
      ];

      testCases.forEach(testCase => {
        // This would test the network detection method
        console.log(`📱 Testing network detection for ${testCase.phone}`);
        // Add actual network detection test here
      });
    });
  });

  describe('Full End-to-End Integration Tests', () => {
    test('should complete full collection-to-webhook flow', async () => {
      const transactionId = `E2E_COLLECTION_${Date.now()}`;

      console.log(`🚀 Starting E2E collection test: ${transactionId}`);

      // Step 1: Create virtual account for merchant (if needed)
      console.log('📋 Step 1: Ensuring merchant virtual account exists');

      // Step 2: Initiate collection via TemboService directly
      console.log('📋 Step 2: Initiating collection via TemboService');
      const collectionResult = await temboService.makeMMPullRequest(
        testConfig.testMerchantId,
        transactionId,
        testConfig.testAmount,
        testConfig.testPhone
      );

      expect(collectionResult.status).toBe(200);
      console.log('✅ Collection initiated successfully');

      // Step 3: Wait for webhook
      console.log('📋 Step 3: Waiting for collection webhook');
      const webhook = await waitForWebhook(transactionId);

      expect(webhook.body.reference).toBe(transactionId);
      expect(webhook.body.amountCredit).toBeGreaterThan(0);
      console.log('✅ Webhook received and validated');

      // Step 4: Process webhook through ThirdPartyHandler
      console.log('📋 Step 4: Processing webhook through ThirdPartyHandler');
      const webhookResult = await thirdPartyHandler.processTemboWebhook(
        webhook.body,
        webhook.headers['x-request-signature'] || 'test_signature',
        webhook.headers['x-request-timestamp'] || Date.now().toString()
      );

      expect(webhookResult.status).toBe(200);
      console.log('✅ Webhook processed successfully');

      console.log(`🎉 E2E collection test completed: ${transactionId}`);
    }, 60000);

    test('should complete full payout-to-webhook flow', async () => {
      const transactionId = `E2E_PAYOUT_${Date.now()}`;

      console.log(`🚀 Starting E2E payout test: ${transactionId}`);

      // Step 1: Initiate payout via TemboService directly
      console.log('📋 Step 1: Initiating payout via TemboService');
      const payoutResult = await temboService.makeMMPushRequest(
        testConfig.testMerchantId,
        transactionId,
        testConfig.testAmount,
        testConfig.testPhone
      );

      expect(payoutResult.status).toBe(200);
      console.log('✅ Payout initiated successfully');

      // Step 2: Wait for webhook
      console.log('📋 Step 2: Waiting for payout webhook');
      const webhook = await waitForWebhook(transactionId);

      expect(webhook.body.reference).toBe(transactionId);
      console.log('✅ Webhook received and validated');

      console.log(`🎉 E2E payout test completed: ${transactionId}`);
    }, 60000);
  });

  describe('Performance and Load Tests', () => {
    test('should handle multiple concurrent collections', async () => {
      const concurrentRequests = 3;
      const promises: Promise<any>[] = [];

      console.log(`🔄 Testing ${concurrentRequests} concurrent collections`);

      for (let i = 0; i < concurrentRequests; i++) {
        const transactionId = `CONCURRENT_${Date.now()}_${i}`;

        const promise = temboService.makeMMPullRequest(
          testConfig.testMerchantId,
          transactionId,
          '500', // Smaller amount for concurrent tests
          testConfig.testPhone
        );

        promises.push(promise);
      }

      const results = await Promise.allSettled(promises);

      // Check that most requests succeeded
      const successful = results.filter(r => r.status === 'fulfilled').length;
      expect(successful).toBeGreaterThan(0);

      console.log(`✅ Concurrent test completed: ${successful}/${concurrentRequests} successful`);
    }, 30000);

    test('should handle API rate limits gracefully', async () => {
      console.log('🚦 Testing rate limit handling');

      // Make rapid requests to test rate limiting
      const rapidRequests = 5;
      const results: any[] = [];

      for (let i = 0; i < rapidRequests; i++) {
        try {
          const result = await temboService.getCollectionBalance();
          results.push({ success: true, result });
        } catch (error) {
          results.push({ success: false, error });
        }

        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      console.log(`📊 Rate limit test results:`, {
        total: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      });

      // Should handle rate limits gracefully (not crash)
      expect(results.length).toBe(rapidRequests);
    }, 20000);
  });

  describe('Data Validation Tests', () => {
    test('should validate transaction amounts correctly', async () => {
      const testAmounts = ['0', '1', '999999', 'invalid', '-100'];

      for (const amount of testAmounts) {
        const transactionId = `AMOUNT_TEST_${Date.now()}_${amount}`;

        try {
          const result = await temboService.makeMMPullRequest(
            testConfig.testMerchantId,
            transactionId,
            amount,
            testConfig.testPhone
          );

          console.log(`💰 Amount ${amount}: ${result.status === 200 ? 'Valid' : 'Invalid'}`);
        } catch (error: any) {
          console.log(`💰 Amount ${amount}: Error - ${error.message}`);
        }
      }
    }, 30000);

    test('should validate phone number formats', async () => {
      const testPhones = [
        '************', // Valid Vodacom
        '255687123456', // Valid Tigo
        '255744123456', // Valid Airtel
        '123456789',    // Invalid
        '+************', // Valid with +
        '0757123456'    // Local format
      ];

      for (const phone of testPhones) {
        const transactionId = `PHONE_TEST_${Date.now()}_${phone.replace(/\+/g, '')}`;

        try {
          const result = await temboService.makeMMPullRequest(
            testConfig.testMerchantId,
            transactionId,
            '1000',
            phone
          );

          console.log(`📱 Phone ${phone}: ${result.status === 200 ? 'Valid' : 'Invalid'}`);
        } catch (error: any) {
          console.log(`📱 Phone ${phone}: Error - ${error.message}`);
        }
      }
    }, 45000);
  });

  describe('Cleanup and Monitoring', () => {
    test('should log all test transactions for monitoring', () => {
      console.log('📊 Test Summary:');
      console.log(`- Webhooks received: ${receivedWebhooks.length}`);
      console.log(`- Test duration: ${Date.now() - testStartTime}ms`);
      console.log(`- Webhook server port: ${webhookPort}`);

      // Log all received webhooks for analysis
      receivedWebhooks.forEach((webhook, index) => {
        console.log(`📥 Webhook ${index + 1}:`, {
          reference: webhook.body.reference,
          amount: webhook.body.amountCredit || webhook.body.amountDebit,
          timestamp: webhook.timestamp
        });
      });

      expect(receivedWebhooks.length).toBeGreaterThan(0);
    });
  });
});
