# Tembo Integration Tests

This directory contains comprehensive integration tests for the Tembo payment provider integration. These tests perform **real API calls** to Tembo's sandbox environment and test the complete payment flow including webhooks.

## 🎯 Test Coverage

### Core Functionality Tests
- ✅ **TemboService Direct Tests**: Test all TemboService methods directly
- ✅ **ThirdPartyHandler Integration**: Test integration through ThirdPartyHandler
- ✅ **Webhook Processing**: Test complete webhook flow end-to-end
- ✅ **Collection Flow**: Test mobile money collections with real webhooks
- ✅ **Payout Flow**: Test mobile money payouts with real webhooks

### Advanced Tests
- ✅ **Error Handling**: Test graceful error handling for invalid inputs
- ✅ **Network Detection**: Test mobile network detection logic
- ✅ **Performance Tests**: Test concurrent requests and rate limiting
- ✅ **Data Validation**: Test amount and phone number validation
- ✅ **End-to-End Flows**: Complete transaction flows from initiation to webhook

## 🚀 Quick Start

### 1. Prerequisites

```bash
# Install dependencies
npm install

# Install test dependencies
npm install --save-dev jest @types/jest ts-jest supertest @types/supertest
```

### 2. Configure Test Environment

Copy the example environment file and configure with your Tembo sandbox credentials:

```bash
cp .env.test.example .env.test
```

Edit `.env.test` with your Tembo sandbox credentials:

```env
# Tembo Sandbox Credentials
TEMBO_AUTH_TOKEN=your_sandbox_auth_token
TEMBO_ACCOUNT_ID=your_sandbox_account_id
TEMBO_SECRET_KEY=your_sandbox_secret_key
TEMBO_WEBHOOK_SECRET=your_sandbox_webhook_secret
TEMBO_DEFAULT_ACCOUNT=your_sandbox_disbursement_account

# Webhook Configuration
WEBHOOK_BASE_URL=http://localhost:3000

# Test Configuration
TEST_PHONE_VODACOM=************
TEST_AMOUNT_SMALL=1000
```

### 3. Run Tests

```bash
# Run all tests
npm test

# Run only integration tests
npm test -- --testNamePattern="integration"

# Run specific test file
npm test -- tembo.integration.test.ts

# Run with verbose output
npm test -- --verbose

# Run with coverage
npm test -- --coverage
```

## 📋 Test Structure

### Test Categories

1. **TemboService Direct Tests**
   - Constructor validation
   - API method calls
   - Balance retrieval
   - Virtual account creation

2. **Collection Flow Tests**
   - Collection initiation via ThirdPartyHandler
   - Webhook reception and processing
   - Merchant fund attribution

3. **Payout Flow Tests**
   - Payout initiation via ThirdPartyHandler
   - Webhook reception and validation

4. **Error Handling Tests**
   - Invalid phone numbers
   - Invalid amounts
   - Webhook signature verification

5. **End-to-End Integration Tests**
   - Complete collection-to-webhook flow
   - Complete payout-to-webhook flow
   - Multi-step transaction processing

6. **Performance Tests**
   - Concurrent request handling
   - Rate limit testing
   - Load testing

## 🔧 Test Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `TEMBO_AUTH_TOKEN` | Tembo API authentication token | ✅ |
| `TEMBO_ACCOUNT_ID` | Tembo account identifier | ✅ |
| `TEMBO_SECRET_KEY` | Tembo API secret key | ✅ |
| `TEMBO_WEBHOOK_SECRET` | Webhook signature secret | ✅ |
| `TEMBO_DEFAULT_ACCOUNT` | Default disbursement account | ✅ |
| `WEBHOOK_BASE_URL` | Base URL for webhooks | ✅ |
| `TEST_PHONE_VODACOM` | Test Vodacom phone number | ❌ |
| `TEST_AMOUNT_SMALL` | Small test amount | ❌ |

### Test Timeouts

- **Short operations**: 5 seconds
- **Medium operations**: 15 seconds  
- **Long operations**: 30 seconds
- **Webhook waiting**: 45 seconds
- **End-to-end flows**: 60 seconds

## 🎯 Test Scenarios

### Collection Test Flow

1. **Initiate Collection**: Call `makeMMPullRequest` with test data
2. **Wait for Webhook**: Listen for Tembo webhook on test server
3. **Process Webhook**: Validate webhook data and signature
4. **Verify Attribution**: Ensure funds are attributed to correct merchant

### Payout Test Flow

1. **Initiate Payout**: Call `makeMMPushRequest` with test data
2. **Wait for Webhook**: Listen for payout confirmation webhook
3. **Validate Status**: Ensure payout was processed successfully

### Webhook Test Server

The tests automatically start a local webhook server that:
- Listens on a random available port
- Captures all incoming webhooks
- Validates webhook structure
- Provides webhook data for test assertions

## 🐛 Debugging Tests

### Enable Debug Logging

```bash
# Set log level to debug
LOG_LEVEL=debug npm test

# Enable API logging
ENABLE_API_LOGGING=true npm test
```

### Common Issues

1. **Webhook Timeouts**
   - Ensure your webhook URL is accessible from Tembo
   - Check firewall settings
   - Verify webhook configuration in Tembo dashboard

2. **Authentication Errors**
   - Verify sandbox credentials are correct
   - Check if credentials have expired
   - Ensure you're using sandbox URLs

3. **Network Errors**
   - Check internet connectivity
   - Verify Tembo sandbox is accessible
   - Check for rate limiting

## 📊 Test Reports

### Coverage Report

```bash
npm test -- --coverage
```

Generates coverage report in `coverage/` directory.

### Test Results

Tests output detailed logs including:
- Transaction IDs for tracking
- Webhook payloads received
- API response details
- Performance metrics

## 🔒 Security Notes

- **Sandbox Only**: These tests use sandbox credentials only
- **No Production Data**: Never use production credentials in tests
- **Credential Rotation**: Rotate test credentials regularly
- **Rate Limiting**: Be mindful of API rate limits during testing

## 🚀 CI/CD Integration

### GitHub Actions Example

```yaml
name: Tembo Integration Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
      - run: npm test
        env:
          TEMBO_AUTH_TOKEN: ${{ secrets.TEMBO_SANDBOX_AUTH_TOKEN }}
          TEMBO_ACCOUNT_ID: ${{ secrets.TEMBO_SANDBOX_ACCOUNT_ID }}
          # ... other secrets
```

## 📞 Support

For issues with:
- **Test Setup**: Check environment configuration
- **Tembo API**: Consult Tembo documentation
- **Webhook Issues**: Verify webhook URL accessibility
- **Test Failures**: Check logs for detailed error information
