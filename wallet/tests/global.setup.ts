/**
 * Global Test Setup
 * 
 * Runs once before all tests start
 */

export default async function globalSetup() {
  console.log('🚀 Starting Tembo integration tests...');
  console.log('📋 Test environment:', process.env.NODE_ENV || 'test');
  console.log('🌐 Tembo API URL:', process.env.TEMBO_API_URL);
  console.log('🔧 Webhook base URL:', process.env.WEBHOOK_BASE_URL);
  
  // Validate test environment
  if (!process.env.TEMBO_AUTH_TOKEN) {
    throw new Error('Test environment not properly configured. Check .env.test file.');
  }
  
  console.log('✅ Global test setup completed');
}
