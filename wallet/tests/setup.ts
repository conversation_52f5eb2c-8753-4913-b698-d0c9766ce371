/**
 * Jest Test Setup
 * 
 * This file runs before each test file and sets up the testing environment
 */

import { config } from 'dotenv';

// Load test environment variables
config({ path: '.env.test' });

// Extend Jest matchers if needed
expect.extend({
  toBeValidTemboResponse(received) {
    const pass = received && 
                 typeof received.status === 'number' && 
                 received.status >= 200 && 
                 received.status < 300;
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid Tembo response`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid Tembo response`,
        pass: false,
      };
    }
  },
});

// Global test configuration
global.testConfig = {
  timeout: {
    short: 5000,
    medium: 15000,
    long: 30000,
    webhook: 45000
  },
  retries: {
    api: 3,
    webhook: 5
  }
};

// Console logging for tests
const originalConsoleLog = console.log;
console.log = (...args: any[]) => {
  const timestamp = new Date().toISOString();
  originalConsoleLog(`[${timestamp}]`, ...args);
};

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Cleanup function
global.cleanup = () => {
  // Add any global cleanup logic here
  console.log('🧹 Running global test cleanup');
};
