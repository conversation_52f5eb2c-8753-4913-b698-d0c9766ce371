module.exports = {
  // Test environment
  testEnvironment: 'node',
  
  // Root directory for tests
  rootDir: '.',
  
  // Test file patterns
  testMatch: [
    '**/tests/**/*.test.ts',
    '**/tests/**/*.integration.test.ts'
  ],
  
  // TypeScript support
  preset: 'ts-jest',
  
  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
  // Transform files
  transform: {
    '^.+\\.ts$': 'ts-jest'
  },
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  
  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts'
  ],
  
  // Test timeout (important for integration tests)
  testTimeout: 60000,
  
  // Verbose output
  verbose: true,
  
  // Environment variables
  setupFiles: ['<rootDir>/tests/env.setup.js'],
  
  // Module path mapping (if needed)
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  
  // Global setup and teardown
  globalSetup: '<rootDir>/tests/global.setup.ts',
  globalTeardown: '<rootDir>/tests/global.teardown.ts',
  
  // Test patterns for different types
  projects: [
    {
      displayName: 'unit',
      testMatch: ['**/tests/**/*.test.ts'],
      testPathIgnorePatterns: ['**/tests/**/*.integration.test.ts']
    },
    {
      displayName: 'integration',
      testMatch: ['**/tests/**/*.integration.test.ts'],
      testTimeout: 120000 // Longer timeout for integration tests
    }
  ]
};
